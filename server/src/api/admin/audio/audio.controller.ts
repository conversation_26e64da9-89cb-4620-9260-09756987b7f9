import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { AudioService } from './audio.service';
import {FileSystemStoredFile, FormDataRequest} from "nestjs-form-data";
import { Groups } from '@/api/user/decorators/groups.decorator';
import { JwtAuthGuard } from '@/api/user/guards/auth.guard';
import { AccessGuard } from '@/api/user/guards/access.guard';

@Controller('admin/audio')
@Groups('LECTURE_MODERATOR')
@UseGuards(JwtAuthGuard, AccessGuard)
export class AudioController {
  constructor(private readonly audioService: AudioService) {}

  @Get()
  async getAll(@Query() filters: any) {
    return await this.audioService.getAll(filters)
  }

  @Get('authors')
  async getAuthors() {
    return await this.audioService.getAuthors()
  }

  @Get('tags')
  async getTags() {
    return await this.audioService.getTags()
  }

  @Post('import')
  @FormDataRequest()
  async import(@Body('file') file: FileSystemStoredFile) {
    return await this.audioService.importFile(file)
  }

  @Get('playlists')
  async getPlaylists() {
    return await this.audioService.getPlaylists()
  }
}
