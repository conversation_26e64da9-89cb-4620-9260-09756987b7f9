import { Body, Controller, Delete, Get, Param, Patch, Post, UseGuards, UsePipes, ValidationPipe } from '@nestjs/common';
import { LibraryService } from './library.service';
import {LibraryCreateDto} from "@/api/admin/library/library.dto";
import {FileSystemStoredFile, FormDataRequest} from "nestjs-form-data";
import { Groups } from '@/api/user/decorators/groups.decorator';
import { JwtAuthGuard } from '@/api/user/guards/auth.guard';
import { AccessGuard } from '@/api/user/guards/access.guard';

@Controller('admin/library')
@Groups('LIBRARY_MODERATOR')
@UseGuards(JwtAuthGuard, AccessGuard)
export class LibraryController {
  constructor(private readonly libraryService: LibraryService) {}

  @Get(':id')
  async get(@Param('id') id: number) {
    return await this.libraryService.get(id)
  }

  @Get()
  async getAll() {
    return await this.libraryService.getAll()
  }

  // @Post()
  // async create(@Body() dto: LibraryCreateDto) {
  //   return await this.libraryService.create(dto)
  // }

  @Post('import')
  @FormDataRequest()
  async import(@Body('file') file: FileSystemStoredFile) {
    return await this.libraryService.import(file)
  }

  @Patch(':id')
  async update(@Param('id') id: number, @Body() dto: LibraryCreateDto) {
    return await this.libraryService.update(id, dto)
  }

  @Delete(':id')
  async delete(@Param('id') id: number) {
    return await this.libraryService.delete(id)
  }
}
