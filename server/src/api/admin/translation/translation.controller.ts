import { Body, Controller, Delete, Get, Param, Patch, Post, UseGuards } from '@nestjs/common';
import { TranslationService } from './translation.service';
import {CreateTranslationDto} from "./translation.dto";
import { Groups } from '@/api/user/decorators/groups.decorator';
import { JwtAuthGuard } from '@/api/user/guards/auth.guard';
import { AccessGuard } from '@/api/user/guards/access.guard';

@Controller('admin/translation')
@Groups('TRANSLATION_MODERATOR')
@UseGuards(JwtAuthGuard, AccessGuard)
export class TranslationController {
  constructor(private readonly translationService: TranslationService) {}

  @Get()
  async getAll() {
    return await this.translationService.getAll()
  }

  @Get(':id')
  async get(@Param('id') id: number) {
    return await this.translationService.get(id)
  }

  @Post('add')
  async create(@Body() dto: CreateTranslationDto) {
    return await this.translationService.create(dto)
  }

  @Patch(':id')
  async update(@Param('id') id: number, @Body() dto: CreateTranslationDto) {
    return await this.translationService.update(id, dto)
  }

  @Delete(':code')
  async delete(@Param('code') code: string) {
    return await this.translationService.delete(code)
  }
}
