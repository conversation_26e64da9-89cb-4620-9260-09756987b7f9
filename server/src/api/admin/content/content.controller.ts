import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ContentService } from './content.service';
import { Groups } from '@/api/user/decorators/groups.decorator';
import { AccessGuard } from '@/api/user/guards/access.guard';
import { JwtAuthGuard } from '@/api/user/guards/auth.guard';

@Controller('admin/content')
@Groups('PAGE_MODERATOR')
@UseGuards(JwtAuthGuard, AccessGuard)
export class ContentController {
  constructor(private readonly contentService: ContentService) {}

  @Get()
  async getAll() {
    return await this.contentService.getAll()
  }

  @Get('tree')
  async getTree() {
    return await this.contentService.getTree()
  }

  @Get('search')
  async search(@Query('query') query: string) {
    return await this.contentService.search(query)
  }

  @Get('category/list')
  async getCategories() {
    return await this.contentService.getCategories()
  }

  @Get('category/:id')
  async getCategory(@Param('id') id: number) {
    return await this.contentService.getCategory(id)
  }

  @Delete('category/:id')
  async deleteCategory(@Param('id') id: number) {
    return await this.contentService.deleteCategory(id)
  }

  @Post('add')
  async create(@Body() dto: any) {
    return await this.contentService.create(dto)
  }

  @Get('link')
  async getLinkedContent(@Query('slug') slug: string) {
    return await this.contentService.getLinkedContent(slug)
  }

  @Get(':slug')
  async getBySlug(@Param('slug') slug: string) {
    return await this.contentService.getBySlug(slug)
  }

  @Patch(':slug')
  async update(@Param('slug') slug: string, @Body() dto: any) {
    return await this.contentService.update(slug, dto)
  }

  @Delete(':slug')
  async delete(
      @Param('slug') slug: string,
      @Query('replace') replace: any,
  ) {
    return await this.contentService.delete(slug, replace)
  }

  @Post('category/add')
  async createCategory(@Body() dto: any) {
    return await this.contentService.createCategory(dto)
  }

  @Post('category/order')
  async updateCategoryOrder(@Body() body: any) {
    return await this.contentService.updateCategoryOrder(body);
  }
}
