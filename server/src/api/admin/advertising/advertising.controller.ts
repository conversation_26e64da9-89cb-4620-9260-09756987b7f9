import { Body, Controller, Delete, Get, Param, Post, UseGuards } from '@nestjs/common';
import { AdvertisingService } from './advertising.service';
import { Groups } from '@/api/user/decorators/groups.decorator';
import { JwtAuthGuard } from '@/api/user/guards/auth.guard';
import { AccessGuard } from '@/api/user/guards/access.guard';

@Controller('/admin/advertising')
@Groups('CALENDAR_MODERATOR')
@UseGuards(JwtAuthGuard, AccessGuard)
export class AdvertisingController {
  constructor(private readonly advertisingService: AdvertisingService) {}

  @Get()
  async getAll() {
    return await this.advertisingService.getAll()
  }

  @Delete(':id')
  async delete(@Param('id') id: number) {
    return await this.advertisingService.delete(id)
  }

  @Post()
  async create(@Body() body: any) {
    return await this.advertisingService.create(body)
  }
}
