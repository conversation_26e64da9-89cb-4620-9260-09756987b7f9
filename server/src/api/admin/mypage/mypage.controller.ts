import { Body, Controller, Delete, Get, Param, Post, UseGuards } from '@nestjs/common';
import { MypageService } from './mypage.service';
import { Groups } from '@/api/user/decorators/groups.decorator';
import { JwtAuthGuard } from '@/api/user/guards/auth.guard';
import { AccessGuard } from '@/api/user/guards/access.guard';

@Controller('admin/mypage')
@Groups('PERSONALPAGE_MODERATOR')
@UseGuards(JwtAuthGuard, AccessGuard)
export class MypageController {
  constructor(private readonly mypageService: MypageService) {}

  @Get()
  async getAll() {
    return await this.mypageService.getAll()
  }

  @Get(':id')
  async getOne(@Param('id') id: number) {
    return await this.mypageService.getOne(id)
  }

  @Post()
  async create(@Body() body: any) {
    return await this.mypageService.create(body)
  }

  @Delete(':id')
  async removePage(@Param('id') id: number) {
    return await this.mypageService.removePage(id)
  }
}
