import { Body, Controller, Delete, Get, Param, Patch, Post, UseGuards } from '@nestjs/common';
import { PhotoService } from './photo.service';
import {FormDataRequest} from "nestjs-form-data";
import {PhotoCreateDto} from "@/api/admin/photo/photo.dto";
import {Photo} from "@/entity/Photo";
import {CreateTranslationDto} from "@/api/admin/translation/translation.dto";
import { Groups } from '@/api/user/decorators/groups.decorator';
import { JwtAuthGuard } from '@/api/user/guards/auth.guard';
import { AccessGuard } from '@/api/user/guards/access.guard';

@Controller('admin/photo')
@Groups('PHOTO_MODERATOR')
@UseGuards(JwtAuthGuard, AccessGuard)
export class PhotoController {
  constructor(private readonly photoService: PhotoService) {}

  @Post()
  @FormDataRequest({fileSystemStoragePath: 'upload/photo'})
  create(@Body() dto: any) {
    return this.photoService.create(dto)
  }

  @Get()
  async getAll() {
    return await Photo.find({
      relations: ['translations', 'translations.photos', 'translations.cover'],
    })
  }

  @Get(':id')
  async get(@Param('id') id: number) {
    return await this.photoService.get(id)
  }

  @Patch(':id')
  async update(@Param('id') id: number, @Body() dto: any) {
    return await this.photoService.update(id, dto)
  }

  @Delete('photos/:id')
  async deletePhoto(@Param('id') id: number) {
    return await this.photoService.deletePhoto(id)
  }

  @Delete(':id')
  async delete(@Param('id') id: number) {
    return await this.photoService.delete(id)
  }

}
