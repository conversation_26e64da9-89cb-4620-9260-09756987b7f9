import { Body, Controller, Delete, Get, Param, Patch, Post, UseGuards } from '@nestjs/common';
import { ConstructorService } from './constructor.service';
import { Groups } from '@/api/user/decorators/groups.decorator';
import { JwtAuthGuard } from '@/api/user/guards/auth.guard';
import { AccessGuard } from '@/api/user/guards/access.guard';

@Controller('admin/constructor')
@Groups('CONSTRUCTOR_MODERATOR')
@UseGuards(JwtAuthGuard, AccessGuard)
export class ConstructorController {
  constructor(private readonly constructorService: ConstructorService) {}

  @Get(':id')
  async get(@Param('id') id: number) {
    return await this.constructorService.get(id)
  }

  @Get()
  async getAll() {
    return await this.constructorService.getAll()
  }

  @Post('create')
  async create(@Body() body: any) {
    return await this.constructorService.create(body)
  }

  @Patch(':id')
  async update(@Param('id') id: number, @Body() body: any) {
    return await this.constructorService.update(body)
  }

  @Delete(':id')
  async delete(@Param('id') id: number) {
    return await this.constructorService.delete(id)
  }
}
