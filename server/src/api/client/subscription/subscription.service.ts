import { BadRequestException, Injectable } from '@nestjs/common';
import { User } from '@/entity/User';
import { PaymentProviderType } from '@/api/client/donation/create-payment.dto';
import { Subscriptions } from '@/common/subscription/subscription.constants';
import { YookassaService } from '@/api/client/donation/payment-providers/yookassa.service';
import { StripeService } from '@/api/client/donation/payment-providers/stripe.service';
import { IPaymentProvider } from '@/api/client/donation/donation.service';
import { UserSubscriptions } from '@/entity/UserSubscriptions';

@Injectable()
export class SubscriptionService {
  private providers: Map<PaymentProviderType, IPaymentProvider> = new Map();

  constructor(
    private readonly yookassaService: YookassaService,
    private readonly stripeService: StripeService,
  ) {
    this.providers.set(PaymentProviderType.YOOKASSA, this.yookassaService);
    this.providers.set(PaymentProviderType.STRIPE, this.stripeService);
  }

  async getList() {
    return Subscriptions;
  }

  async pay(body: any, user: User) {
    const subscription = Subscriptions[body.type];
    const payment = body.payment;
    const autoRenew: boolean = !!body.autoRenew;

    if (!subscription) {
      throw new BadRequestException('Подписка не найдена');
    }

    const provider = this.providers.get(payment);
    if (!provider) {
      throw new BadRequestException('Выбранный способ оплаты не поддерживается.');
    }

    let currency: string;
    if (payment === PaymentProviderType.STRIPE) currency = 'EUR';
    else if (payment === PaymentProviderType.YOOKASSA) currency = 'RUB';
    else throw new BadRequestException('Валюта не поддерживается.');

    const price = payment === 'stripe' ? subscription.price.eur : subscription.price.rub;
    const description = `Оплата подписки ${subscription.name}, пользователь ${user.firstName} ${user.lastName}`;

    const metadata = {
      module: 'subscriptions',
      value: body.type,
      userId: user.id,
      autoRenew: autoRenew ? '1' : '0',
    };

    if (payment === PaymentProviderType.STRIPE && autoRenew) {
      if (!subscription.stripePriceId) {
        throw new BadRequestException('Stripe priceId не настроен для подписки');
      }
      return (this.stripeService as any).createSubscriptionCheckout(
        subscription.stripePriceId,
        user.email,
        description,
        metadata,
      );
    }

    return provider.createPayment(price, currency, description, metadata);
  }

  async onSubscriptionPaid(body: any) {
    // Stripe
    if (body?.type === 'checkout.session.completed' || body?.type === 'invoice.payment_succeeded') {
      const dataObj = body?.data?.object;
      const metadata = dataObj?.metadata || dataObj?.payment_intent?.metadata;

      if (metadata?.module !== 'subscriptions') return { status: 'error' };

      const userId = Number(metadata.userId);
      const user = await User.findOne({ where: { id: userId }, relations: ['subscriptions'] });
      if (!user) return { status: 'error' };

      const subType = metadata.value;
      let record = user.subscriptions.find(s => s.type === subType);

      if (!record) {
        record = new UserSubscriptions();
        record.type = subType;
        user.subscriptions.push(record);
      }

      record.provider = 'stripe';
      record.isAutoRenew = metadata.autoRenew === '1';

      if (body?.type === 'checkout.session.completed' && dataObj?.mode === 'subscription') {
        record.stripeSubscriptionId = dataObj?.subscription;
        record.paymentId = dataObj?.payment_intent || dataObj?.id;
        record.currentPeriodEnd = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
      }

      if (body?.type === 'invoice.payment_succeeded') {
        record.currentPeriodEnd = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
      }

      await record.save();
      return await user.save();
    }

    // ЮKassa
    if (body?.event === 'payment.succeeded') {
      const dataObj = body?.object || body?.data?.object;
      const metadata = dataObj?.metadata;

      if (metadata?.module !== 'subscriptions') return { status: 'error' };

      const paymentId = dataObj?.id;
      if (await UserSubscriptions.findOneBy({ paymentId })) {
        return { status: 'ok' };
      }

      const userId = Number(metadata.userId);
      const user = await User.findOne({ where: { id: userId }, relations: ['subscriptions'] });
      if (!user) return { status: 'error' };

      const subType = metadata.value;

      const newSubscription = new UserSubscriptions();
      newSubscription.type = subType;
      newSubscription.paymentId = paymentId;
      newSubscription.provider = 'yookassa';
      newSubscription.isAutoRenew = metadata.autoRenew === '1';
      newSubscription.currentPeriodEnd = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

      const pmId = dataObj?.payment_method?.id;
      if (newSubscription.isAutoRenew && pmId) {
        newSubscription.yookassaPaymentMethodId = pmId;
      }

      await newSubscription.save();
      user.subscriptions.push(newSubscription);
      return await user.save();
    }

    return { status: 'ignored' };
  }

  async cancelAutoRenew(subscriptionId: number, user: User) {
    const record = await UserSubscriptions.findOne({ where: { id: subscriptionId, user: { id: user.id } } });
    if (!record) throw new BadRequestException('Подписка не найдена');

    record.isAutoRenew = false;

    if (record.provider === 'stripe' && record.stripeSubscriptionId) {
      const stripe = this.providers.get(PaymentProviderType.STRIPE)
      await stripe.cancelAutoRenew(record.stripeSubscriptionId)
    }

    await record.save();
    return { success: true };
  }


}
