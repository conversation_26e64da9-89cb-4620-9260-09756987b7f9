import { Module } from '@nestjs/common';
import { ContentModule } from "./content/content.module";
import { TranslationModule } from './translation/translation.module';
import { LibraryModule } from './library/library.module';
import { PhotoModule } from './photo/photo.module';
import { AudioModule } from './audio/audio.module';
import { ProfileModule } from './profile/profile.module';
import { PlaylistModule } from './playlist/playlist.module';
import { ConstructorModule } from './constructor/constructor.module';
import { SearchModule } from './search/search.module';
import { SitemapModule } from './sitemap/sitemap.module';
import { MypageModule } from './mypage/mypage.module';
import { ForumModule } from './forum/forum.module';
import { AdvertisingModule } from './advertising/advertising.module';
import { AudiofilesModule } from './audiofiles/audiofiles.module';
import { DonationModule } from './donation/donation.module';
import { SubscriptionModule } from './subscription/subscription.module';

@Module({
    imports: [ContentModule, TranslationModule, LibraryModule, PhotoModule, AudioModule, ProfileModule, PlaylistModule, ConstructorModule, SearchModule, SitemapModule, MypageModule, ForumModule, AdvertisingModule, AudiofilesModule, DonationModule, SubscriptionModule],
    controllers: [],
    providers: [],
})
export class ClientModule {}
