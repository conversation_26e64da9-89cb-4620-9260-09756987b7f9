import { Injectable } from '@nestjs/common';
import {Content} from "@/entity/Content";
import {Audio} from "@/entity/Audio";
import {LibraryTranslation} from "@/entity/LibraryTranslation";
import { create } from 'xmlbuilder2';
import * as moment from 'moment';

@Injectable()
export class SitemapService {
    async generateSitemap(lang: string) {
        const result = []
        const entities: any = [
            { name: 'Страницы', entity: Content, lang: true, relations: ['category'] },
            { name: 'Лекции', entity: Audio, relations: [] },
            { name: 'Книги', entity: LibraryTranslation, lang: true, relations: [] },
        ]

        for(let item of entities) {
            const queryOptions: any = {};
            
            if (item.lang) {
                queryOptions.where = { lang };
            }
            
            if (item.relations && item.relations.length > 0) {
                queryOptions.relations = item.relations;
            }
            
            const data = await item.entity.find(queryOptions);
            
            if(!data.length) continue;
            result.push({
                entity: item.name,
                items: data
            })
        }

        return result
    }

    async generateSitemapXml(): Promise<string> {
        const domain = 'https://dev.advayta.org';
        const urlset = create({ version: '1.0', encoding: 'UTF-8' }).ele('urlset', {
            xmlns: 'http://www.sitemaps.org/schemas/sitemap/0.9',
        });

        const entities: any = [
            { entity: Content, path: 'categories/1', changefreq: 'weekly', priority: 0.8 },
            { entity: Audio, path: 'audiogallery/audiolektsii', changefreq: 'weekly', priority: 0.7 },
            { entity: LibraryTranslation, path: 'library', changefreq: 'weekly', priority: 0.7 },
        ];

        const staticPages = [
            { loc: `${domain}/`, changefreq: 'daily', priority: 1.0 }
        ];

        for (const page of staticPages) {
            const urlElement = urlset.ele('url');
            urlElement.ele('loc').txt(page.loc);
            urlElement.ele('changefreq').txt(page.changefreq);
            urlElement.ele('priority').txt(page.priority.toString());
        }

        for (const item of entities) {
            const data = await item.entity.find();
            if (!data.length) continue;

            for (const entry of data) {
                const url = {
                    loc: `${domain}/${entry.lang || 'ru'}/${item.path}/${entry.slug || entry.code || entry.external_id}`,
                    lastmod: entry.created_at ? moment(entry.created_at).format('YYYY-MM-DD') : undefined,
                    changefreq: item.changefreq,
                    priority: item.priority,
                };

                const urlElement = urlset.ele('url');
                urlElement.ele('loc').txt(url.loc);
                if (url.lastmod) {
                    urlElement.ele('lastmod').txt(url.lastmod);
                }
                urlElement.ele('changefreq').txt(url.changefreq);
                urlElement.ele('priority').txt(url.priority.toString());
            }
        }

        return urlset.end({ prettyPrint: true });
    }
}
