import { Controller, Get, Header, Query } from '@nestjs/common';
import { SitemapService } from './sitemap.service';

@Controller()
export class SitemapController {
  constructor(private readonly sitemapService: SitemapService) {}

  @Get('client/sitemap')
  async generateSitemap(@Query('lang') lang: string) {
    return await this.sitemapService.generateSitemap(lang)
  }

  @Get('sitemap.xml')
  @Header('Content-Type', 'application/xml')
  async getSitemap() {
    return this.sitemapService.generateSitemapXml();
  }
}
