import { Module } from '@nestjs/common';
import { UserService } from './user.service';
import { UserController } from './user.controller';
import {TypeOrmModule} from "@nestjs/typeorm";
import {UserGroup} from "@/entity/UserGroup";
import {User} from "@/entity/User";
import {JwtModule} from "@nestjs/jwt";
import {JwtStrategy} from "./jwt.strategy";
import {PassportModule} from "@nestjs/passport";
import {Favourite} from "@/entity/Favourite";
import {Like} from "@/entity/Like";
import {GoogleStrategy} from "@/api/user/google.strategy";
import { FileService } from '../file/file.service';
import { HttpModule, HttpService } from '@nestjs/axios';
import { YookassaService } from '@/api/client/donation/payment-providers/yookassa.service';
import { StripeService } from '@/api/client/donation/payment-providers/stripe.service';
import { UserSubscriptions } from '@/entity/UserSubscriptions';

@Module({
  imports: [
      PassportModule,
      TypeOrmModule.forFeature([User, UserGroup, Favourite, Like, UserSubscriptions]),
      JwtModule.register({
        secret: 'advaytasecret',
        signOptions: {
          expiresIn: '1h'
        }
    }),
    HttpModule
  ],
  controllers: [UserController],
  providers: [UserService, JwtStrategy, GoogleStrategy, FileService],
})
export class UserModule {}
