import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import {ValidationPipe} from "@nestjs/common";
import * as cookieParser from 'cookie-parser';
import {ExceptionsFilter} from "@/filters/exceptions.filter";
import * as compression from 'compression';
import {json} from "express";
import { ConfigModule } from '@nestjs/config';

async function bootstrap() {

  const app = await NestFactory.create(AppModule);
  ConfigModule.forRoot();
  app.use(cookieParser());
  app.enableCors({
    origin: true,
    credentials: true,
  })
  app.setGlobalPrefix('api', {
    exclude: ['sitemap.xml']
  })
  app.useGlobalPipes(
      new ValidationPipe({
        transform: true
      })
  );
  //app.useGlobalFilters(new ExceptionsFilter());
  app.use(compression({
    threshold: 0
  }));
  app.use(json({ limit: '50mb' }));
  await app.listen(process.env.SERVER_PORT ?? 3000);
}
bootstrap();
