import {
    BaseEntity,
    Column, CreateDateColumn,
    Entity,
    JoinColumn,
    JoinTable,
    ManyToMany,
    ManyToOne,
    OneToOne,
    PrimaryGeneratedColumn
} from "typeorm";
import {Library} from "@/entity/Library";
import {File} from "@/entity/File";
import {User} from "@/entity/User";
import {AudioTag} from "@/entity/AudioTag";

@Entity()
export class LibraryTranslation extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @CreateDateColumn()
    created_at: Date;

    @Column()
    external_id: string;

    @Column()
    lang: string;

    @Column()
    code: string

    @Column()
    title: string

    @Column({nullable: true})
    author: string;

    @Column({nullable: true})
    reader: string;

    @Column()
    seo_title: string

    @Column()
    seo_description: string

    @Column({nullable: true})
    pages: string

    @Column({nullable: true})
    category: string

    @Column({nullable: true})
    recomendation: string

    @Column({nullable: true})
    image: string

    @Column({nullable: true})
    access: string

    @Column({nullable: true})
    annotation: string

    @Column({nullable: true})
    content: string

    @Column({nullable: true})
    format: string

    @Column({nullable: true})
    link: string

    @Column({nullable: true})
    summary: string

    @Column({nullable: true, type: 'json', default: []})
    audio: string

    @ManyToMany(() => AudioTag, tag => tag.audios)
    @JoinTable()
    tags: AudioTag[];

    @ManyToOne(() => Library, library => library.translations, {cascade: true, onUpdate: 'CASCADE', onDelete: 'CASCADE'})
    library: LibraryTranslation;

    @Column({default: 0})
    views: number

    @ManyToMany(() => User)
    @JoinTable({name: 'library_likes'})
    likes: User[]

    @Column({nullable: true})
    tagsString: string

    @Column({nullable: true})
    linkShop: string

    @Column({nullable: true})
    linkShopOnline: string

    @Column({nullable: true})
    duration: string

    @Column({nullable: true, type: 'simple-json', default: []})
    chapters: []

    @Column({default: false})
    paid: boolean
}