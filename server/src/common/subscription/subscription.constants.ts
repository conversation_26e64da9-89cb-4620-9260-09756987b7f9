export const Subscriptions = {
  AUDIO: {
    name: 'Аудио',
    price: {
      rub: 1200,
      eur: 12
    },
    components: ['AUDIO'] as const,
  },
  LIBRARY: {
    name: 'Библиотека',
    price: {
      rub: 1500,
      eur: 15
    },
    components: ['LIBRARY'] as const,
  },
  COURSES: {
    name: 'Курсы',
    price: {
      rub: 2500,
      eur: 25
    },
    components: ['COURSES'] as const,
  },
  AUDIO_AND_LIBRARY: {
    name: 'Аудио + Библиотека',
    price: {
      rub: 2200,
      eur: 22
    },
    components: ['AUDIO', 'LIBRARY'] as const,
  },
  AUDIO_AND_COURSES: {
    name: 'Аудио + Курсы',
    price: {
      rub: 3200,
      eur: 32
    },
    components: ['AUDIO', 'COURSES'] as const,
  },
  LIBRARY_AND_COURSES: {
    name: 'Библио<PERSON>ека + Курсы',
    price: {
      rub: 3500,
      eur: 35
    },
    components: ['LIBRARY', 'COURSES'] as const,
  },
  FULL_ACCESS: {
    name: 'Полный доступ',
    price: {
      rub: 3900,
      eur: 39
    },
    components: ['AUDIO', 'LIBRARY', 'COURSES'] as const,
  },
} as const

export type SubscriptionType = keyof typeof Subscriptions;