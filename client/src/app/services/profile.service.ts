import {CookieService} from "ngx-cookie-service";
import {inject, Injectable, signal} from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {FormGroup} from "@angular/forms";
import {Track} from "@/interfaces/track";
import {BehaviorSubject, tap} from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class ProfileService {
  private http = inject(HttpClient)
  private cookieService: CookieService = inject(CookieService);

  name = signal(this.cookieService.get('name'))
  avatar = signal(this.cookieService.get('avatar'))
  profile: {
    firstName: string,
    lastName: string,
    spiritualName: string,
    email: string,
    id: number,
    avatar: {
      name: string
    }
    favourites: {
      id: number,
      audio: {
        id: number,
        title: string,
        description: string,
        external_id: string,
        author: string,
        date: string
      }
    }[],
    playlists: {
      id: number,
      name: string,
      items: Track[]
    }[],
    libraryFavourites: {
      id: number
    }[],
    libraryLikes: {
      id: number
    }[],
    photoFavourites: {
      id: number,
      name: string,
      description: string,
    }[],
    favouriteContent: [],
    likesContent: {
      id: number,
      parent_id?: number | null
    }[],
    topicFavorites: any,
    topicCommentFavorites: any,
    quoteFavourites: {
      id: number,
      quote: string,
      library: {
        id: number,
        title: string
      }
    }[],
    quoteFavouritesContent: {
      id: number,
      quote: string,
      content: {
        id: number,
        title: string
      }
    }[]
    audioListened: {
      date: string,
      count: number,
      audio: {
        id: number,
      } | null
    }[],
    subscriptions: []
  } | null = null

  public data$ = new BehaviorSubject(null);


  get isAuth() {
    return this.cookieService.get('token')
  }

  getProfile() {
    return this.http.get('/user/profile').pipe(
      tap((res: any) => {
        this.profile = res;
        this.data$.next(res)
        this.name.set(this.profile?.spiritualName || this.profile?.firstName || this.profile?.email || 'Пользователь');
        this.avatar.set(this.profile?.avatar?.name || '');
      })
    )
  }

  update(form: FormGroup) {
    return this.http.patch('/user/profile', form.value).pipe()
  }

  removeFromFavourites(id: number) {
    return this.http.delete('/user/profile/favourites/' + id).pipe(
      tap(() => this.getProfile().subscribe())
    )
  }

  getStatuses() {
    return this.http.get('/user/statuses')
  }

  setProfile() {
    if(this.isAuth)
      this.getProfile().subscribe();
  }

  getPlaylist() {
    return this.http.get('/user/playlist')
  }

  getSubscriptions() {
    return this.http.get('/client/subscriptions')
  }

  paySubscription(form: any) {
    return this.http.post('/client/subscriptions/pay', form)
  }

  cancelAutoRenew(subscriptionId: number) {
    return this.http.post('/client/subscriptions/cancel-auto-renew', { subscriptionId });
  }
}
