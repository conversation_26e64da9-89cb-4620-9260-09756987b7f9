:host::ng-deep {
    .search-modal {
        transition: .5s;
    }

    .search-opened {
        transition: .5s;
        top: 0;
    }
}

.hesd_ {
    display: none;
}

.m_grid.nav_wrap {
    max-width: fit-content;
    width: fit-content;
}

// Стили для выпадающего меню пользователя
.user-menu-container {
    position: relative;
}

.user-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: rgba(255, 255, 255, 1);
    border: 1px solid var(--text-color);
    border-radius: 15px;
    min-width: 160px;
    z-index: 1000;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-top: 5px;
}

.dropdown-item {
    padding: 12px 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    font-family: Prata;
    font-weight: 400;
    font-size: 17px;
    line-height: 20px;
    color: var(--font-color1);
    transition: background-color 0.2s;

    &:hover {
        background-color: var(--selection, rgba(255, 227, 164, 0.3));
    }

    &:not(:last-child) {
        border-bottom: 1px solid rgba(209, 144, 54, 0.2);
    }

    span {
        white-space: nowrap;
    }
}

@media (max-width: 768px) {
    .m_grid.nav_wrap {
        max-width: 100%;
        width: 100%;
    }

    .sidebar_open {
        display: none;
    }

    .user-dropdown-menu {
        right: -10px;
        min-width: 140px;
    }

    .dropdown-item {
        padding: 10px 16px;
        font-size: 16px;
    }
}