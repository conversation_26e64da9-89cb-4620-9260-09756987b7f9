import {FormBuilder, ReactiveFormsModule, Validators} from "@angular/forms";
import {passwordMatchValidator} from '@/validators/validators'
import {AuthService} from "@/services/auth.service";
import {Component, inject} from '@angular/core';
import {CommonModule} from "@angular/common";
import {ActivatedRoute, Router, RouterModule} from "@angular/router";
import { ToasterService } from "@/services/toaster.service";
import {environment} from "@/env/environment";
import { ScCheckboxReCaptcha } from '@semantic-components/re-captcha';

@Component({
  selector: 'registrationForm',
  standalone: true,
  imports: [ReactiveFormsModule, CommonModule, RouterModule, ScCheckboxReCaptcha],
  templateUrl: './registration-form.component.html',
  styleUrl: './registration-form.component.scss'
})
export class RegistrationFormComponent {
  authService = inject(AuthService)
  route = inject(ActivatedRoute)
  router = inject(Router)
  return = this.route.snapshot.queryParams['return'];
  fb = inject(FormBuilder)
  registrationForm = this.fb.group({
    email: ['', Validators.required],
    password: ['', Validators.required],
    confirmPassword: ['', Validators.required],
    captcha: ['', Validators.required],
  }, {validators: passwordMatchValidator})
  errors = null
  success = false
  toasterService = inject(ToasterService);
  showPasswordState: boolean = false;
  showConfirmPasswordState: boolean = false;

  registrationFormSubmit() {
    this.errors = null
    this.success = false
    if(!this.registrationForm.valid) return
    this.authService.signUp(this.registrationForm.value).subscribe({
      next: response => {
        this.success = true
        this.toasterService.showToast('Успешная регистрация!', 'success', 'bottom-middle', 3000);
        this.registrationForm.reset()
        this.router.navigate(['/ru/signin'], {queryParams: {return: this.return}});
      },
      error: response => {
        this.errors = response.error
        this.toasterService.showToast(response.error.message, 'error', 'bottom-middle');
      }
    })
  }

  signInGoogle() {
    location.href = environment.serverUrl + '/api/user/google'
  }

  showPassword() {
    this.showPasswordState = !this.showPasswordState;
  }

  showPassInConfirm() {
    this.showConfirmPasswordState = !this.showConfirmPasswordState;
  }
}
