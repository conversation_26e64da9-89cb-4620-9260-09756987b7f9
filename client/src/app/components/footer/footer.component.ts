import { Component, EventEmitter, Inject, inject, Output, PLATFORM_ID } from '@angular/core';
import { Router } from "@angular/router";
import { CommonModule } from '@angular/common';
import { TranslocoService, TranslocoPipe } from "@jsverse/transloco";
import { ToasterService } from '@/services/toaster.service';

@Component({
  selector: 'app-footer',
  standalone: true,
  imports: [ CommonModule],
  templateUrl: './footer.component.html',
  styleUrl: './footer.component.scss'
})
export class FooterComponent {
  @Output() sideOpen = new EventEmitter<any>();
  router = inject(Router);
  translocoService = inject(TranslocoService);

  constructor(
    @Inject(PLATFORM_ID) private platformId: any,
    private toasterService: ToasterService
  ) { }

  ngOnInit() {
  }

  navigateToLibrary() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/library`]);
    this.sideOpen.emit(false);
  }

  navigateToPhoto() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/photo`]);
    this.sideOpen.emit(false);
  }

  navigateToAudio() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/audiogallery/audiolektsii`]);
    this.sideOpen.emit(false);
  }

  navigateToForum() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/forum`]);
    this.sideOpen.emit(false);
  }
}
