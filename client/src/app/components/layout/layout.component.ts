import { TranslocoService} from "@jsverse/transloco";
import {Component, inject, PLATFORM_ID} from '@angular/core';
import {ActivatedRoute, RouterOutlet} from "@angular/router";
import {isPlatformBrowser} from "@angular/common";
import {ProfileService} from "@/services/profile.service";

@Component({
  selector: 'app-layout',
  standalone: true,
  imports: [
    RouterOutlet,
  ],
  templateUrl: './layout.component.html',
  styleUrl: './layout.component.css'
})
export class LayoutComponent {
  route = inject(ActivatedRoute)
  translocoService = inject(TranslocoService)
  platformId = inject(PLATFORM_ID)
  profileService = inject(ProfileService)
  showSearch: boolean = false;

  ngOnInit() {
    if(isPlatformBrowser(this.platformId)) {
      // this.profileService.getProfile().subscribe()
    }
    this.route.params.subscribe(params => {
      this.translocoService.setActiveLang(params['lang'])
    })
  }
}
