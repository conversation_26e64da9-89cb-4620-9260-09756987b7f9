import { Component, inject, Inject, PLATFORM_ID } from '@angular/core';
import { Router } from "@angular/router";
import { CommonModule, isPlatformBrowser, Location } from '@angular/common';
import { BreadcrumbComponent } from '../breadcrumb/breadcrumb.component';

@Component({
  selector: 'app-goback',
  standalone: true,
  imports: [ CommonModule,
    BreadcrumbComponent],
  templateUrl: './goback.component.html',
  styleUrl: './goback.component.scss'
})
export class GobackComponent {
  router = inject(Router);

  constructor(
    @Inject(PLATFORM_ID) private platformId: any,
    private location: Location,
  ) { }

  ngOnInit() { }

  goBack(): void {
    if (isPlatformBrowser(this.platformId)) {
      if (window.history.length > 1) {
        this.location.back();
      } else {
        this.router.navigate(['/']);
      }
    } else {
      console.log('Back button not available in SSR.');
    }
  }
}
