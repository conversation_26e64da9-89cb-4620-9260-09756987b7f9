export interface Track {
    link: string,
    audioStatus: string,
    authorName?: string,
    author: string,
    comment: string,
    date: string,
    description: string,
    duration: string,
    fullDescription: string,
    external_id: string,
    scriptures: {},
    title: string,
    type: string,
    videoStatus: string,
    youtube: string,
    views: number,
    likes: number,
    id: number,
    liked: boolean,
    inFavourites: boolean,
    time?: number,
    paid?: boolean
}
