import { Component, inject } from '@angular/core';
import {SitemapService} from "@/services/sitemap.service";
import {CommonModule} from "@angular/common";
import {TranslocoService} from "@jsverse/transloco";
import { GobackComponent } from '@/components/goback/goback.component';
import { Router } from '@angular/router';

@Component({
  selector: 'app-sitemap',
  standalone: true,
  imports: [
    CommonModule,
    GobackComponent
  ],
  templateUrl: './sitemap.component.html',
  styleUrl: './sitemap.component.scss'
})
export class SitemapComponent {
  router = inject(Router);
  translocoService: TranslocoService = inject(TranslocoService);
  sitemapService: SitemapService = inject(SitemapService);
  sitemap: any = []
  ngOnInit() {
    this.sitemapService.generateSitemap().subscribe((res: any) => this.sitemap = res);
  }

  getLink(item: any, link: any) {
    const lang = this.translocoService.getActiveLang()
    let url = ''
    switch (item.entity) {
      case 'Лекции':
        url = `/${lang}/audiogallery/audiolektsii/${link.external_id}`
        break;
      case 'Книги':
        url = `/${lang}/library/${link.code}`
        break;
      default:
        url = `/${lang}/categories/${link.category.id}/${link.slug}`
        break;
    }
    return url;
  }

  navigateTo(item: any, link: string) {
    this.router.navigate([this.getLink(item, link)]);
  }
}
