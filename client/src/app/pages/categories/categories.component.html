<div>
  <div class="middle_stripe">
    <breadcrumb></breadcrumb>
    <div class="wrapper_line">
      <div class="dec_head _background">
        <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
        <h1 class="dec_head-title_">Категории</h1>
        <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      </div>
      <div class="categories mar_md">
        @for(category of categories; track category.id) {
        <div class="categ_wrapper">
          <div class="cat-add_w hoverable-area"
            (click)="$event.stopPropagation();router.navigate(['/'+translocoService.getActiveLang()+'/categories/' + category.id])">
            <div class="catg_wp">
              @if(category.preview) {
              <img style="object-fit: cover" width="210" height="143"
                [ngSrc]="environment.serverUrl + '/upload/' + category.preview.name">
              } @else {
              <img src="assets/images/clouds.avif">
              }
              <div class="overlay"></div>
            </div>
            <div class="category_title">{{category.title}}</div>
          </div>
        </div>
        }
      </div>
    </div>
  </div>
</div>