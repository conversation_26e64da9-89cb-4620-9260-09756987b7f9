import {Component, inject} from '@angular/core';
import {ContentService} from "@/services/content.service";
import {NgOptimizedImage} from "@angular/common";
import {environment} from "@/env/environment";
import {TranslocoService} from "@jsverse/transloco";
import {Router} from "@angular/router";
import { BreadcrumbComponent } from '@/components/breadcrumb/breadcrumb.component';

@Component({
  selector: 'app-categories',
  standalone: true,
  imports: [
    NgOptimizedImage,
    BreadcrumbComponent
  ],
  templateUrl: './categories.component.html',
  styleUrl: './categories.component.scss'
})
export class CategoriesComponent {
  contentService = inject(ContentService);
  translocoService = inject(TranslocoService);
  router = inject(Router);
  categories: any

  ngOnInit() {
    this.contentService.getCategories().subscribe(res => this.categories = res);
  }

  protected readonly environment = environment;
}
