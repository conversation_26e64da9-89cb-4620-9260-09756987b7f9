<div class="playlist-wrapper">
  <!-- <div class="playlist-btns">
    <button [routerLink]="['/ru/profile/playlist/add']" class="button_b">Создать плейлист</button>
  </div> -->
  <dialog #modal>
    <div>
      {{message}}
    </div>
    <div class="text-center mt-6">
      <button (click)="closeModal(modal)" class="cancel-button">Да</button>
    </div>
  </dialog>
  <dialog class="stylized_wide" #confirmDialog>
    <div class="dialog-message">
      {{ message }}
    </div>
    <div class="mt-[60px] flex gap-[16px] text-center justify-center dialog-footer">
      <button type="submit" class="confirm-btn ok-button">
        <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
        <div class="confirm-btn-label">Да</div>
      </button>
      <button type="submit" class="confirm-btn cancel-button">
        <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
        <div class="confirm-btn-label">Отмена</div>
      </button>
      <!-- <button class="ok-button">Да</button>
      <button class="cancel-button ml-4">Отмена</button> -->
    </div>
  </dialog>
    @if(selectedPlayList) {
      <div class="selected-playlist flex justify-between items-center">
        <div class="flex flex-col gap-3">
          <div class="playlist-name">
            {{selectedPlayList.name}}
          </div>
          <div *ngIf="selectedPlayList?.items?.length" class="playlist-count">
            {{selectedPlayList.items.length}} аудио
          </div>
        </div>
        <div class="playlist-head-actions flex gap-[20px]">
          <button class="back-to-playlists-btn" (click)="selectedPlayList=null">
            <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M7.77014 1.95468C7.77014 2.20276 7.65477 2.39325 7.48172 2.56159C6.02631 4.0102 4.57534 5.45881 3.12436 6.91184C2.85369 7.18207 2.77826 7.49217 2.92469 7.79784C2.97794 7.90859 3.06224 8.01491 3.15099 8.09908C4.58421 9.5344 6.02187 10.9697 7.45953 12.405C7.88994 12.8347 7.88551 13.2512 7.4551 13.6809C7.37079 13.765 7.28648 13.8536 7.1933 13.9378C6.88269 14.2125 6.44341 14.2125 6.13724 13.9334C6.00856 13.8138 5.88432 13.6853 5.76008 13.5613C3.94525 11.7494 2.13043 9.94196 0.320037 8.13009C-0.105937 7.70481 -0.105937 7.28396 0.3156 6.86311C2.22361 4.96265 4.13161 3.05775 6.03518 1.15728C6.45228 0.740866 6.87382 0.740866 7.29092 1.15728C7.37966 1.24588 7.47284 1.33448 7.55715 1.42751C7.69914 1.5737 7.77014 1.7509 7.77014 1.95468Z" fill="var(--text-color)"/>
              <path d="M6.24219 7.4922C6.24219 7.25741 6.34424 7.07135 6.50842 6.90744C7.50236 5.91512 8.4963 4.9228 9.49024 3.93049C10.4176 3.00462 11.345 2.07875 12.2724 1.15288C12.6895 0.736463 13.1066 0.740893 13.5281 1.15731C13.6124 1.24148 13.6923 1.32122 13.7722 1.40539C14.0695 1.71549 14.0783 2.1452 13.7988 2.46859C13.75 2.52175 13.7012 2.57491 13.6479 2.62364C12.228 4.04123 10.8037 5.45883 9.38374 6.88086C8.97108 7.29285 8.97552 7.70484 9.38818 8.12126C10.8303 9.561 12.2679 10.9963 13.71 12.4361C13.9319 12.6576 14.0739 12.9189 13.9674 13.2246C13.8165 13.6543 13.5059 13.9644 13.0755 14.1195C12.8048 14.2169 12.543 14.1195 12.3301 13.9113C11.9485 13.5258 11.5624 13.1449 11.1808 12.7639C9.62779 11.2134 8.07476 9.65846 6.5173 8.1124C6.35756 7.9352 6.24219 7.74471 6.24219 7.4922Z" fill="var(--text-color)"/>
            </svg>
            <span class="btn-label">
              Вернуться в плейлисты
            </span>
          </button>
          <button class="play-all-btn">
            <svg width="12" height="15" viewBox="0 0 12 15" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M11.6541 6.86495C12.1153 7.1358 12.1153 7.8642 11.6541 8.13505L0.960271 14.4145C0.525241 14.6699 3.49471e-07 14.3225 3.73213e-07 13.7794L9.22176e-07 1.22059C9.45918e-07 0.677449 0.525242 0.330097 0.960272 0.585548L11.6541 6.86495Z" fill="var(--text-color)"/>
            </svg>
            <span (click)="play(selectedPlayList.items)" class="btn-label">
              Воспроизвести все
            </span>
          </button>
        </div>
      </div>
      <div class="playlist-item__list pt-[40px]">
        @if(selectedPlayList.items.length) {
          @for(track of selectedPlayList.items; track track.id; let i = $index) {
            <div class="flex justify-between items-start track-item" (drop)="onDrop($event, i)" (dragover)="onDragOver($event)">
              <div class="track-main-info flex gap-[30px]">
                <div class="track-left-icons flex gap-[21px]">
                  <div class="drag-btn cursor-pointer pt-2" draggable="true" (dragstart)="onDragStart(i, $event)">
                    <svg width="14" height="20" viewBox="0 0 14 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <ellipse cx="11.7031" cy="18.1819" rx="1.81816" ry="1.81842" transform="rotate(90 11.7031 18.1819)"
                        fill="var(--text-color)" />
                      <ellipse cx="11.7031" cy="9.99931" rx="1.81816" ry="1.81842" transform="rotate(90 11.7031 9.99931)"
                        fill="var(--text-color)" />
                      <ellipse cx="11.7031" cy="1.81816" rx="1.81816" ry="1.81842" transform="rotate(90 11.7031 1.81816)"
                        fill="var(--text-color)" />
                      <ellipse cx="1.81732" cy="18.1819" rx="1.81816" ry="1.81842" transform="rotate(90 1.81732 18.1819)"
                        fill="var(--text-color)" />
                      <ellipse cx="1.81732" cy="9.99931" rx="1.81816" ry="1.81842" transform="rotate(90 1.81732 9.99931)"
                        fill="var(--text-color)" />
                      <ellipse cx="1.81732" cy="1.81816" rx="1.81816" ry="1.81842" transform="rotate(90 1.81732 1.81816)"
                        fill="var(--text-color)" />
                    </svg>
                  </div>

                  <svg (click)="play([track])" class="play-icon cursor-pointer" width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 36C27.9411 36 36 27.9411 36 18C36 8.05887 27.9411 0 18 0C8.05887 0 0 8.05887 0 18C0 27.9411 8.05887 36 18 36Z" fill="url(#paint0_linear_1470_8286)"/>
                    <g clip-path="url(#clip0_1470_8286)">
                    <path d="M14.1349 11.7695C14.2289 11.7695 14.3228 11.7695 14.4262 11.7695C14.708 11.8235 14.9711 11.9224 15.2154 12.0572C17.9212 13.5498 20.6364 15.0333 23.3422 16.5258C23.5395 16.6337 23.7274 16.7685 23.8871 16.9214C24.3287 17.335 24.3475 17.8654 23.9529 18.324C23.7838 18.5218 23.5677 18.6656 23.3328 18.7915C20.627 20.275 17.9212 21.7675 15.206 23.251C15.0087 23.3589 14.7926 23.4399 14.5765 23.5028C13.9846 23.6556 13.4867 23.4039 13.2706 22.8554C13.1672 22.6037 13.1484 22.343 13.1484 22.0732C13.1578 19.1242 13.1578 16.1751 13.1578 13.2351C13.1578 13.1182 13.1578 13.0013 13.1766 12.8754C13.2142 12.5517 13.3176 12.255 13.5712 12.0213C13.7309 11.8774 13.9282 11.8145 14.1349 11.7695Z" fill="white"/>
                    </g>
                    <defs>
                    <linearGradient id="paint0_linear_1470_8286" x1="1.83824e-07" y1="18" x2="36" y2="18" gradientUnits="userSpaceOnUse">
                    <stop stop-color="var(--pl_start1)"/>
                    <stop offset="1" stop-color="var(--pl_stop1)"/>
                    </linearGradient>
                    <clipPath id="clip0_1470_8286">
                    <rect width="11.0769" height="11.7692" fill="white" transform="translate(13.1562 11.7695)"/>
                    </clipPath>
                    </defs>
                  </svg>
                </div>

                <div class="flex flex-col gap-2">
                  <div class="track-name">
                    {{track.title}}
                  </div>
                  <div class="track-bottom-info flex gap-[30px]">
                    <div class="track-author">{{track.author}}</div>
                    <div class="track-time-info flex gap-[30px]">
                      <div class="flex gap-3 items-center">
                        <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M10.3125 0C10.7708 0 11.2292 0 11.6875 0C11.7448 0.0114823 11.7964 0.0287056 11.8536 0.0344468C12.5411 0.15501 13.2573 0.195198 13.9161 0.401879C18.2589 1.73956 20.9057 4.64457 21.8281 9.10543C21.9083 9.50156 21.9427 9.90919 22 10.3111C22 10.7704 22 11.2296 22 11.6889C21.9885 11.7463 21.9714 11.798 21.9656 11.8554C21.851 13.3653 21.4672 14.8064 20.7281 16.1326C18.6484 19.8413 15.4802 21.8278 11.2292 21.9943C8.94323 22.0861 6.82344 21.4488 4.92135 20.1686C2.33177 18.4118 0.744792 15.9948 0.171875 12.9175C0.0973958 12.5099 0.0572917 12.0966 0 11.6889C0 11.2296 0 10.7704 0 10.3111C0.0114583 10.2479 0.0286458 10.1848 0.034375 10.1216C0.108854 9.62213 0.154688 9.11117 0.257812 8.61743C1.14583 4.35177 4.78385 0.872651 9.08073 0.172234C9.49323 0.10334 9.9 0.0574113 10.3125 0ZM1.76458 11.8612C2.17708 16.5115 6.00417 19.9217 10.1406 20.226C10.1406 19.9849 10.1406 19.7437 10.1406 19.4969C10.1464 18.9687 10.5016 18.5955 11 18.5955C11.4984 18.5955 11.8536 18.9687 11.8594 19.4969C11.8594 19.738 11.8594 19.9791 11.8594 20.226C16.3052 19.9045 19.9375 16.1096 20.2182 11.8612C20.1094 11.8612 20.0063 11.8612 19.8974 11.8612C18.9865 11.8612 18.5911 11.5971 18.5911 10.9943C18.5969 10.4029 18.9865 10.1388 19.8859 10.1388C20.0005 10.1388 20.1094 10.1388 20.224 10.1388C19.8458 5.43111 15.8469 2.00939 11.8594 1.78549C11.8594 2.02088 11.8594 2.25626 11.8594 2.49165C11.8536 3.03132 11.4984 3.41023 10.9885 3.40449C10.4958 3.39875 10.1406 3.02557 10.1406 2.49165C10.1406 2.25052 10.1406 2.00939 10.1406 1.76827C5.27083 2.19311 1.97656 6.32672 1.7875 10.1446C2.01094 10.1446 2.22865 10.1446 2.45208 10.1446C3.01927 10.1446 3.3974 10.489 3.40313 11C3.40313 11.5167 3.025 11.8612 2.44062 11.8669C2.22292 11.8612 1.99948 11.8612 1.76458 11.8612Z" fill="var(--text-color)"/>
                          <path d="M10.1408 8.4682C10.1408 7.64722 10.1408 6.8205 10.1408 5.99952C10.1408 5.45985 10.5018 5.0752 11.0002 5.0752C11.4986 5.0752 11.8596 5.45985 11.8596 6.00526C11.8596 7.47499 11.8653 8.93897 11.8539 10.4087C11.8539 10.5809 11.8997 10.7015 12.02 10.8221C12.994 11.7866 13.9565 12.7568 14.9247 13.7213C15.1309 13.928 15.2627 14.1691 15.2341 14.4677C15.1997 14.8179 15.0163 15.0705 14.6841 15.1968C14.3346 15.3288 14.0195 15.2485 13.7559 14.9844C13.1429 14.3816 12.5414 13.773 11.9341 13.1644C11.4528 12.6822 10.9773 12.1999 10.4903 11.7234C10.2497 11.488 10.1351 11.2239 10.1408 10.8852C10.1466 10.07 10.1408 9.26622 10.1408 8.4682Z" fill="var(--text-color)"/>
                        </svg>
                        <div class="track-time">5 мин.</div>
                      </div>
                      <div class="flex gap-3 items-center">
                        <svg width="20" height="22" viewBox="0 0 20 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M5.87682 4.60465C5.45728 4.60465 5.10938 4.25674 5.10938 3.83721V0.767442C5.10938 0.347907 5.45728 0 5.87682 0C6.29635 0 6.64426 0.347907 6.64426 0.767442V3.83721C6.64426 4.25674 6.29635 4.60465 5.87682 4.60465Z" fill="var(--text-color)"/>
                          <path d="M14.0721 4.60465C13.6526 4.60465 13.3047 4.25674 13.3047 3.83721V0.767442C13.3047 0.347907 13.6526 0 14.0721 0C14.4917 0 14.8396 0.347907 14.8396 0.767442V3.83721C14.8396 4.25674 14.4917 4.60465 14.0721 4.60465Z" fill="var(--text-color)"/>
                          <path d="M6.39044 13.5582C6.25742 13.5582 6.1244 13.5275 6.00161 13.4763C5.86858 13.4251 5.76626 13.3535 5.66393 13.2614C5.47975 13.067 5.36719 12.8112 5.36719 12.5349C5.36719 12.4019 5.39789 12.2689 5.44905 12.1461C5.50021 12.0233 5.57184 11.9107 5.66393 11.8084C5.76626 11.7163 5.86858 11.6447 6.00161 11.5935C6.36998 11.44 6.83044 11.5219 7.11695 11.8084C7.30114 12.0028 7.4137 12.2689 7.4137 12.5349C7.4137 12.5963 7.40347 12.6679 7.39323 12.7396C7.383 12.801 7.36254 12.8623 7.33184 12.9237C7.31137 12.9851 7.28068 13.0465 7.23975 13.1079C7.20905 13.1591 7.15789 13.2103 7.11695 13.2614C6.92254 13.4456 6.65649 13.5582 6.39044 13.5582Z" fill="var(--text-color)"/>
                          <path d="M9.97638 13.5581C9.84336 13.5581 9.71033 13.5274 9.58754 13.4762C9.45452 13.425 9.35219 13.3534 9.24987 13.2613C9.06568 13.0669 8.95312 12.8111 8.95312 12.5348C8.95312 12.4018 8.98382 12.2688 9.03499 12.146C9.08615 12.0232 9.15778 11.9106 9.24987 11.8083C9.35219 11.7162 9.45452 11.6446 9.58754 11.5934C9.95592 11.4297 10.4164 11.5218 10.7029 11.8083C10.8871 12.0027 10.9996 12.2688 10.9996 12.5348C10.9996 12.5962 10.9894 12.6678 10.9792 12.7395C10.9689 12.8009 10.9485 12.8623 10.9178 12.9237C10.8973 12.985 10.8666 13.0464 10.8257 13.1078C10.795 13.159 10.7438 13.2102 10.7029 13.2613C10.5085 13.4455 10.2424 13.5581 9.97638 13.5581Z" fill="var(--text-color)"/>
                          <path d="M13.5545 13.5581C13.4215 13.5581 13.2885 13.5274 13.1657 13.4762C13.0326 13.425 12.9303 13.3534 12.828 13.2613C12.7871 13.2102 12.7461 13.159 12.7052 13.1078C12.6643 13.0464 12.6336 12.985 12.6131 12.9237C12.5824 12.8623 12.5619 12.8009 12.5517 12.7395C12.5415 12.6678 12.5312 12.5962 12.5312 12.5348C12.5312 12.2688 12.6438 12.0027 12.828 11.8083C12.9303 11.7162 13.0326 11.6446 13.1657 11.5934C13.5443 11.4297 13.9945 11.5218 14.281 11.8083C14.4652 12.0027 14.5778 12.2688 14.5778 12.5348C14.5778 12.5962 14.5675 12.6678 14.5573 12.7395C14.5471 12.8009 14.5266 12.8623 14.4959 12.9237C14.4754 12.985 14.4447 13.0464 14.4038 13.1078C14.3731 13.159 14.3219 13.2102 14.281 13.2613C14.0866 13.4455 13.8206 13.5581 13.5545 13.5581Z" fill="var(--text-color)"/>
                          <path d="M6.39044 17.1393C6.25742 17.1393 6.1244 17.1087 6.00161 17.0576C5.87882 17.0064 5.76626 16.9347 5.66393 16.8426C5.47975 16.6482 5.36719 16.3821 5.36719 16.1161C5.36719 15.9831 5.39789 15.85 5.44905 15.7272C5.50021 15.5942 5.57184 15.4818 5.66393 15.3897C6.04254 15.0111 6.73835 15.0111 7.11695 15.3897C7.30114 15.5841 7.4137 15.85 7.4137 16.1161C7.4137 16.3821 7.30114 16.6482 7.11695 16.8426C6.92254 17.0268 6.65649 17.1393 6.39044 17.1393Z" fill="var(--text-color)"/>
                          <path d="M9.97638 17.1393C9.71033 17.1393 9.44429 17.0268 9.24987 16.8426C9.06568 16.6482 8.95312 16.3821 8.95312 16.1161C8.95312 15.9831 8.98382 15.85 9.03499 15.7272C9.08615 15.5942 9.15778 15.4818 9.24987 15.3897C9.62847 15.0111 10.3243 15.0111 10.7029 15.3897C10.795 15.4818 10.8666 15.5942 10.9178 15.7272C10.9689 15.85 10.9996 15.9831 10.9996 16.1161C10.9996 16.3821 10.8871 16.6482 10.7029 16.8426C10.5085 17.0268 10.2424 17.1393 9.97638 17.1393Z" fill="var(--text-color)"/>
                          <path d="M13.5545 17.1395C13.2885 17.1395 13.0224 17.0269 12.828 16.8427C12.7359 16.7506 12.6643 16.6381 12.6131 16.505C12.5619 16.3822 12.5312 16.2492 12.5312 16.1162C12.5312 15.9832 12.5619 15.8502 12.6131 15.7274C12.6643 15.5943 12.7359 15.4818 12.828 15.3897C13.0633 15.1543 13.4215 15.0418 13.7489 15.1134C13.8206 15.1236 13.8819 15.1441 13.9433 15.1748C14.0047 15.1953 14.0661 15.226 14.1275 15.2669C14.1787 15.2976 14.2299 15.3488 14.281 15.3897C14.4652 15.5841 14.5778 15.8502 14.5778 16.1162C14.5778 16.3822 14.4652 16.6483 14.281 16.8427C14.0866 17.0269 13.8206 17.1395 13.5545 17.1395Z" fill="var(--text-color)"/>
                          <path d="M18.6706 8.78952H1.27525C0.855719 8.78952 0.507812 8.44162 0.507812 8.02208C0.507812 7.60255 0.855719 7.25464 1.27525 7.25464H18.6706C19.0901 7.25464 19.438 7.60255 19.438 8.02208C19.438 8.44162 19.0901 8.78952 18.6706 8.78952Z" fill="var(--text-color)"/>
                          <path d="M14.0698 21.9998H5.88372C2.14884 21.9998 0 19.8509 0 16.1161V7.41839C0 3.68351 2.14884 1.53467 5.88372 1.53467H14.0698C17.8046 1.53467 19.9535 3.68351 19.9535 7.41839V16.1161C19.9535 19.8509 17.8046 21.9998 14.0698 21.9998ZM5.88372 3.06955C2.95721 3.06955 1.53488 4.49188 1.53488 7.41839V16.1161C1.53488 19.0426 2.95721 20.4649 5.88372 20.4649H14.0698C16.9963 20.4649 18.4186 19.0426 18.4186 16.1161V7.41839C18.4186 4.49188 16.9963 3.06955 14.0698 3.06955H5.88372Z" fill="var(--text-color)"/>
                        </svg>
                        <div class="track-date">05.02.25</div>
                      </div>
                    </div>
                  </div>
                </div>

              </div>
              <div class="mobile-actions icons_w" (mouseleave)="closeMobileActionSelect()">
                <div class="icon-wrap star_w" (click)="showMobileActionOptions(track)">
                  <svg class="emty_f" width="7" height="22" viewBox="0 0 7 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="3.27778" cy="3.27778" r="2.27778" stroke="var(--font-color1)"/>
                    <circle cx="3.27778" cy="11.2499" r="2.27778" stroke="var(--font-color1)"/>
                    <circle cx="3.27778" cy="19.2221" r="2.27778" stroke="var(--font-color1)"/>
                  </svg>
                  <svg class="emty_f_hover" width="7" height="22" viewBox="0 0 7 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="3.27778" cy="3.27778" r="2.27778" fill="var(--text-color)" stroke="var(--text-color)"/>
                    <circle cx="3.27778" cy="11.2499" r="2.27778" fill="var(--text-color)" stroke="var(--text-color)"/>
                    <circle cx="3.27778" cy="19.2221" r="2.27778" fill="var(--text-color)" stroke="var(--text-color)"/>
                  </svg>
                </div>
                <div *ngIf="selectedDropdElement?.id === track.id" class="dropdown-content-wrapper">
                  <div class="dropdown-content">
                    <div class="dropdown-content-inner">
                      @for(action of selectedPlaylistsActions; track action) {
                        <div class="dropdown-item flex gap-2 items-center" (click)="onClickMobileAction(track, action, 'track')">
                          <ng-container *ngTemplateOutlet="dropdItemIcon; context: {action: action, item: track}"></ng-container>
                          {{action}}
                        </div>
                      }
                    </div>
                  </div>
                </div>
              </div>
              <div class="desktop-actions actions_w gap-[30px]">
                <div class="flex items-center cursor-pointer icons_w shr_hov" (click)="share(track)">
                  <div class="icon-wrap share_w">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M15.9238 7.01009C13.5066 8.21941 11.0965 9.42157 8.66504 10.638C8.9368 11.5325 8.94395 12.427 8.66504 13.3357C11.0822 14.5451 13.4923 15.7544 15.8737 16.9422C16.2456 16.5988 16.5745 16.2267 16.9607 15.9476C19.6353 13.9941 23.4542 15.6113 23.9334 18.8957C24.3481 21.7509 22.0168 24.2268 19.1347 23.9835C17.0894 23.8117 15.3731 22.1516 15.1514 20.1051C15.1085 19.6829 15.1156 19.2464 15.1729 18.8313C15.2086 18.588 15.1442 18.5093 14.9512 18.4163C12.8772 17.3859 10.8105 16.3483 8.73656 15.3107C8.50056 15.1891 8.25741 15.0817 8.02141 14.9458C7.87838 14.8671 7.79972 14.8885 7.6853 15.0102C6.76276 15.9834 5.63283 16.4556 4.28836 16.4413C2.21445 16.4127 0.326467 14.7454 0.0475605 12.6846C-0.288557 10.2588 1.18464 8.12638 3.58752 7.63979C5.16084 7.32494 6.52676 7.79722 7.65669 8.94213C7.80687 9.09241 7.89984 9.10672 8.07862 9.01369C10.3742 7.85447 12.677 6.70955 14.9798 5.56464C15.1585 5.47877 15.2158 5.40005 15.1729 5.18538C14.7295 2.75959 16.4458 0.39105 18.8773 0.047575C21.4232 -0.317367 23.7117 1.45725 23.9763 3.99753C24.2266 6.37323 22.4673 8.57004 20.093 8.8348C18.4196 9.028 17.0751 8.41977 16.0239 7.12458C15.9881 7.09596 15.9595 7.06018 15.9238 7.01009ZM7.14179 11.9976C7.14179 10.5021 5.91174 9.26414 4.42424 9.2713C2.94389 9.27845 1.721 10.4949 1.70669 11.9762C1.69239 13.4574 2.92244 14.7025 4.41709 14.7168C5.90459 14.7311 7.14179 13.5003 7.14179 11.9976ZM16.832 19.5541C16.8248 21.0496 18.0549 22.2876 19.5424 22.2804C21.0227 22.2804 22.2671 21.0353 22.2671 19.5541C22.2599 18.0728 21.0513 16.8635 19.5567 16.8492C18.062 16.8277 16.8391 18.0442 16.832 19.5541ZM19.5424 7.14605C21.037 7.14605 22.2671 5.91527 22.2671 4.42688C22.2599 2.93849 21.0156 1.70055 19.5352 1.7077C18.0477 1.71486 16.8463 2.93133 16.832 4.41972C16.8248 5.92242 18.0406 7.14605 19.5424 7.14605Z"
                        fill="var(--font-color1)" />
                    </svg>
                  </div>
                  <div class="on_hov">
                    поделиться
                  </div>
                </div>
                <button class="flex items-center icons_w lik_hov"
                  [ngClass]="{'is-liked': isLiked(track)}"
                  (click)="like(track)">
                  <div class="icon-wrap like_w">
                    <svg class="emty_l" width="24" height="22" viewBox="0 0 24 22" fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                        fill="var(--font-color1)" />
                    </svg>
                    <svg class="emty_l_hover" width="24" height="22" viewBox="0 0 24 22" fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                        fill="var(--text-color)" />
                    </svg>
                  </div>
                  <div class="on_hov">
                    мне нравится
                  </div>
                </button>
                <div class="flex items-center cursor-pointer icons_w fav_hov"
                (click)="removeTrackFromPlaylist(selectedPlayList.id, track.id)">
                  <div class="icon-wrap star_w">
                    <svg class="emty_f" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9.41339 0C9.80471 0 10.196 0 10.5874 0C10.723 0.015656 10.8587 0.0313121 10.9891 0.0469681C13.1388 0.27659 15.0328 1.0907 16.6451 2.53106C18.3721 4.07579 19.4469 5.99104 19.8487 8.28204C19.9165 8.65779 19.953 9.03875 20 9.4145C20 9.8059 20 10.1973 20 10.5887C19.9843 10.7296 19.9687 10.8705 19.953 11.0114C19.7182 13.1093 18.9565 14.988 17.5373 16.5537C14.8293 19.5335 11.4796 20.5877 7.56112 19.6901C5.26535 19.163 3.4183 17.8792 2.00432 15.9953C0.439025 13.9182 -0.197528 11.5594 0.052919 8.98135C0.261625 6.86256 1.08601 4.98906 2.50522 3.39736C4.04964 1.6491 5.97495 0.563617 8.27594 0.15656C8.65161 0.0887176 9.0325 0.0521868 9.41339 0ZM3.52787 15.2385C7.4359 11.3298 11.3335 7.4314 15.2363 3.52783C12.4135 1.14811 7.5559 0.761927 4.17486 4.05491C1.01818 7.12872 0.877308 11.9977 3.52787 15.2385ZM16.4729 4.76465C12.5648 8.67345 8.66204 12.577 4.76446 16.4754C7.76461 18.9699 12.6066 19.1369 15.8676 15.9013C19.0556 12.7336 19.04 7.89064 16.4729 4.76465Z" fill="var(--font-color1)"/>
                      </svg>
                    <svg class="emty_f_hover" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9.41339 0C9.80471 0 10.196 0 10.5874 0C10.723 0.015656 10.8587 0.0313121 10.9891 0.0469681C13.1388 0.27659 15.0328 1.0907 16.6451 2.53106C18.3721 4.07579 19.4469 5.99104 19.8487 8.28204C19.9165 8.65779 19.953 9.03875 20 9.4145C20 9.8059 20 10.1973 20 10.5887C19.9843 10.7296 19.9687 10.8705 19.953 11.0114C19.7182 13.1093 18.9565 14.988 17.5373 16.5537C14.8293 19.5335 11.4796 20.5877 7.56112 19.6901C5.26535 19.163 3.4183 17.8792 2.00432 15.9953C0.439025 13.9182 -0.197528 11.5594 0.052919 8.98135C0.261625 6.86256 1.08601 4.98906 2.50522 3.39736C4.04964 1.6491 5.97495 0.563617 8.27594 0.15656C8.65161 0.0887176 9.0325 0.0521868 9.41339 0ZM3.52787 15.2385C7.4359 11.3298 11.3335 7.4314 15.2363 3.52783C12.4135 1.14811 7.5559 0.761927 4.17486 4.05491C1.01818 7.12872 0.877308 11.9977 3.52787 15.2385ZM16.4729 4.76465C12.5648 8.67345 8.66204 12.577 4.76446 16.4754C7.76461 18.9699 12.6066 19.1369 15.8676 15.9013C19.0556 12.7336 19.04 7.89064 16.4729 4.76465Z" fill="var(--text-color)"/>
                    </svg>

                  </div>
                  <div class="on_hov">
                    удалить
                  </div>
                </div>
                <div class="flex items-center cursor-pointer icons_w fav_hov" (click)="addToPlaylist(track)">
                  <div class="icon-wrap star_w">
                    <svg class="emty_f" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M6.40263 10.2479C8.21852 10.2479 11.491 10.2479 13.3069 10.2479C13.7952 10.2479 14.1614 10.5368 14.2454 10.9701C14.3217 11.3601 14.0775 11.779 13.6655 11.909C13.5205 11.9596 13.3527 11.974 13.2001 11.974C12.3608 11.9813 11.5215 11.974 10.6746 11.974C7.96607 11.974 3.80851 11.974 1.09994 11.974C0.932081 11.974 0.764226 11.9668 0.61163 11.9163C0.207252 11.7935 -0.0521606 11.3818 0.0088776 10.9846C0.0699158 10.5801 0.420886 10.2696 0.855783 10.2407C0.932081 10.2335 1.00838 10.2407 1.07705 10.2407C2.85479 10.2479 4.62489 10.2479 6.40263 10.2479Z" fill="var(--font-color1)"/>
                      <path d="M6.37913 1.74056C4.60902 1.74056 2.83129 1.74056 1.06118 1.74056C0.923842 1.74056 0.778876 1.73334 0.64154 1.69C0.244792 1.58167 -0.0146208 1.22056 0.00063879 0.823335C0.0235281 0.433334 0.321089 0.101111 0.725468 0.0288889C0.832285 0.00722224 0.946731 0 1.05355 0C4.61665 0 15.3472 0 18.9103 0C19.3604 0 19.7114 0.137223 19.8945 0.54889C20.1539 1.12667 19.7038 1.73334 19.0171 1.74056C17.8497 1.74778 16.69 1.74056 15.5227 1.74056C14.8589 1.74056 7.03529 1.74056 6.37913 1.74056Z" fill="var(--font-color1)"/>
                      <path d="M6.37913 6.8586C4.60902 6.8586 2.83129 6.8586 1.06118 6.8586C0.923842 6.8586 0.778876 6.85138 0.64154 6.80805C0.244792 6.69971 -0.0146208 6.3386 0.00063879 5.94138C0.0235281 5.55138 0.321089 5.21915 0.725468 5.14693C0.832285 5.12526 0.946731 5.11804 1.05355 5.11804C4.61665 5.11804 15.3472 5.11804 18.9103 5.11804C19.3604 5.11804 19.7114 5.25526 19.8945 5.66693C20.1539 6.24471 19.7038 6.85138 19.0171 6.8586C17.8497 6.86582 16.69 6.8586 15.5227 6.8586C14.8589 6.8586 7.03529 6.8586 6.37913 6.8586Z" fill="var(--font-color1)"/>
                      <path d="M3.66601 17.3046C2.75043 17.3046 1.83486 17.3046 0.919288 17.3046C0.49965 17.3046 0.16394 17.0591 0.041863 16.6835C-0.0725837 16.3296 0.0494928 15.9468 0.385203 15.7518C0.553058 15.6507 0.774322 15.5857 0.965066 15.5857C2.76569 15.5713 6.98048 15.5713 8.78111 15.5785C9.35335 15.5785 9.73484 15.9613 9.72721 16.4668C9.71958 16.9579 9.3152 17.3046 8.75822 17.3046C7.87317 17.3118 4.55869 17.3046 3.66601 17.3046Z" fill="var(--font-color1)"/>
                      <path d="M16.3557 17.5185C15.4402 17.5185 15.1395 17.5185 14.224 17.5185C13.8043 17.5185 13.4686 17.2729 13.3466 16.8973C13.2321 16.5435 13.3542 16.1607 13.6899 15.9657C13.8577 15.8646 14.079 15.7996 14.2698 15.7996C16.0704 15.7851 17.2484 15.7851 19.049 15.7923C19.6213 15.7923 20.0028 16.1751 19.9951 16.6807C19.9875 17.1718 19.5831 17.5185 19.0262 17.5185C18.1411 17.5257 17.2484 17.5185 16.3557 17.5185Z" fill="var(--font-color1)"/>
                      <path d="M15.7613 16.3604C15.7613 15.4448 15.7613 15.1442 15.7613 14.2286C15.7613 13.809 16.0069 13.4733 16.3824 13.3512C16.7363 13.2367 17.1191 13.3588 17.3141 13.6945C17.4152 13.8624 17.4802 14.0836 17.4802 14.2744C17.4947 16.075 17.4947 17.2531 17.4874 19.0537C17.4874 19.6259 17.1047 20.0074 16.5991 19.9998C16.108 19.9922 15.7613 19.5878 15.7613 19.0308C15.7541 18.1457 15.7613 17.2531 15.7613 16.3604Z" fill="var(--font-color1)"/>
                    </svg>
                    <svg class="emty_f_hover" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M6.40263 10.2479C8.21852 10.2479 11.491 10.2479 13.3069 10.2479C13.7952 10.2479 14.1614 10.5368 14.2454 10.9701C14.3217 11.3601 14.0775 11.779 13.6655 11.909C13.5205 11.9596 13.3527 11.974 13.2001 11.974C12.3608 11.9813 11.5215 11.974 10.6746 11.974C7.96607 11.974 3.80851 11.974 1.09994 11.974C0.932081 11.974 0.764226 11.9668 0.61163 11.9163C0.207252 11.7935 -0.0521606 11.3818 0.0088776 10.9846C0.0699158 10.5801 0.420886 10.2696 0.855783 10.2407C0.932081 10.2335 1.00838 10.2407 1.07705 10.2407C2.85479 10.2479 4.62489 10.2479 6.40263 10.2479Z" fill="var(--text-color)"/>
                      <path d="M6.37913 1.74056C4.60902 1.74056 2.83129 1.74056 1.06118 1.74056C0.923842 1.74056 0.778876 1.73334 0.64154 1.69C0.244792 1.58167 -0.0146208 1.22056 0.00063879 0.823335C0.0235281 0.433334 0.321089 0.101111 0.725468 0.0288889C0.832285 0.00722224 0.946731 0 1.05355 0C4.61665 0 15.3472 0 18.9103 0C19.3604 0 19.7114 0.137223 19.8945 0.54889C20.1539 1.12667 19.7038 1.73334 19.0171 1.74056C17.8497 1.74778 16.69 1.74056 15.5227 1.74056C14.8589 1.74056 7.03529 1.74056 6.37913 1.74056Z" fill="var(--text-color)"/>
                      <path d="M6.37913 6.8586C4.60902 6.8586 2.83129 6.8586 1.06118 6.8586C0.923842 6.8586 0.778876 6.85138 0.64154 6.80805C0.244792 6.69971 -0.0146208 6.3386 0.00063879 5.94138C0.0235281 5.55138 0.321089 5.21915 0.725468 5.14693C0.832285 5.12526 0.946731 5.11804 1.05355 5.11804C4.61665 5.11804 15.3472 5.11804 18.9103 5.11804C19.3604 5.11804 19.7114 5.25526 19.8945 5.66693C20.1539 6.24471 19.7038 6.85138 19.0171 6.8586C17.8497 6.86582 16.69 6.8586 15.5227 6.8586C14.8589 6.8586 7.03529 6.8586 6.37913 6.8586Z" fill="var(--text-color)"/>
                      <path d="M3.66601 17.3046C2.75043 17.3046 1.83486 17.3046 0.919288 17.3046C0.49965 17.3046 0.16394 17.0591 0.041863 16.6835C-0.0725837 16.3296 0.0494928 15.9468 0.385203 15.7518C0.553058 15.6507 0.774322 15.5857 0.965066 15.5857C2.76569 15.5713 6.98048 15.5713 8.78111 15.5785C9.35335 15.5785 9.73484 15.9613 9.72721 16.4668C9.71958 16.9579 9.3152 17.3046 8.75822 17.3046C7.87317 17.3118 4.55869 17.3046 3.66601 17.3046Z" fill="var(--text-color)"/>
                      <path d="M16.3557 17.5185C15.4402 17.5185 15.1395 17.5185 14.224 17.5185C13.8043 17.5185 13.4686 17.2729 13.3466 16.8973C13.2321 16.5435 13.3542 16.1607 13.6899 15.9657C13.8577 15.8646 14.079 15.7996 14.2698 15.7996C16.0704 15.7851 17.2484 15.7851 19.049 15.7923C19.6213 15.7923 20.0028 16.1751 19.9951 16.6807C19.9875 17.1718 19.5831 17.5185 19.0262 17.5185C18.1411 17.5257 17.2484 17.5185 16.3557 17.5185Z" fill="var(--text-color)"/>
                      <path d="M15.7613 16.3604C15.7613 15.4448 15.7613 15.1442 15.7613 14.2286C15.7613 13.809 16.0069 13.4733 16.3824 13.3512C16.7363 13.2367 17.1191 13.3588 17.3141 13.6945C17.4152 13.8624 17.4802 14.0836 17.4802 14.2744C17.4947 16.075 17.4947 17.2531 17.4874 19.0537C17.4874 19.6259 17.1047 20.0074 16.5991 19.9998C16.108 19.9922 15.7613 19.5878 15.7613 19.0308C15.7541 18.1457 15.7613 17.2531 15.7613 16.3604Z" fill="var(--text-color)"/>
                    </svg>

                  </div>
                  <div class="on_hov">
                    воспроизвести
                  </div>
                </div>
              </div>
            </div>
            <!-- <div class="playlist-track">
              <div class="playlist-track__name"><span>{{track.date}}</span> <strong>{{track.title || 'Без названия'}}</strong></div>
              <div class="playlist-track__duration">{{formatTime(+track.duration)}}</div>
              <div class="playlist-play" (click)="play([track])"></div>
              <div class="playlist-track__delete" (click)="removeTrackFromPlaylist(selectedPlayList.id, track.id)">Удалить</div>
            </div> -->
          }
        } @else {
          <div class="playlist-item__empty">
            Для добавления аудио в плейлист перейдите на страницу с лекциями,<br> выберите необходимую аудиолекцию и нажмите кнопку "Добавить в плейлист"
          </div>
        }
      </div>
    } @else {

      @if(playlist.length) {
        <div class="playlist">
          @for(item of playlist; track item.id) {
            <div class="playlist-item" (click)="selectPlaylist(item)">
              <div class="flex justify-between">
                <!-- <input (input)="renamePlaylist(item.id, item.name)" type="text" [(ngModel)]="item.name"> -->
                <div class="flex flex-col gap-3">
                  <div class="playlist-name">
                    {{item.name}}
                  </div>
                  <div *ngIf="item?.items?.length" class="playlist-count">
                    {{item.items.length}} аудио
                  </div>
                </div>
                <div class="mobile-actions icons_w" (mouseleave)="closeMobileActionSelect()">
                  <div class="icon-wrap star_w" (click)="showMobileActionOptions(item); $event.stopPropagation()">
                    <svg class="emty_f" width="7" height="22" viewBox="0 0 7 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="3.27778" cy="3.27778" r="2.27778" stroke="var(--font-color1)"/>
                      <circle cx="3.27778" cy="11.2499" r="2.27778" stroke="var(--font-color1)"/>
                      <circle cx="3.27778" cy="19.2221" r="2.27778" stroke="var(--font-color1)"/>
                    </svg>
                    <svg class="emty_f_hover" width="7" height="22" viewBox="0 0 7 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="3.27778" cy="3.27778" r="2.27778" fill="var(--text-color)" stroke="var(--text-color)"/>
                      <circle cx="3.27778" cy="11.2499" r="2.27778" fill="var(--text-color)" stroke="var(--text-color)"/>
                      <circle cx="3.27778" cy="19.2221" r="2.27778" fill="var(--text-color)" stroke="var(--text-color)"/>
                    </svg>
                  </div>
                  @if (selectedDropdElement?.id === item.id) {
                    <div class="dropdown-content-wrapper">
                      <div class="dropdown-content">
                        <div class="dropdown-content-inner">
                          @for(action of playlistsActions; track action) {
                            <div class="dropdown-item flex gap-2 items-center" (click)="onClickMobileAction(item, action, 'playlist')">
                              <ng-container *ngTemplateOutlet="dropdItemIcon; context: {action: action, item: item}"></ng-container>
                              {{action}}
                            </div>
                          }
                        </div>
                      </div>
                    </div>
                  }
                </div>
                <div class="desktop-actions actions_w gap-[25px]">
                  <div *ngIf="item.name !== 'Послушать позже'" class="flex items-center cursor-pointer icons_w t_p fav_hov"
                  (click)="$event.stopPropagation()">
                    <div class="icon-wrap star_w">
                      <svg  class="emty_f" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M5.0748 15.1053L5.57859 14.9053C5.80948 14.8105 5.9984 14.6737 6.14534 14.4737C6.51268 13.9895 6.89052 13.5053 7.25786 13.0316L7.63569 12.5474C8.18146 11.8421 8.71673 11.1474 9.26249 10.4421C10.5114 8.82106 11.8129 7.13685 13.0933 5.47369C14.0064 4.28422 13.9645 2.70527 12.9884 1.64211C12.663 1.28421 12.2642 0.989475 11.8759 0.705264C11.8024 0.652632 11.7184 0.589474 11.645 0.536843C11.1622 0.178948 10.5954 0 9.96568 0H9.94469C8.95812 0.0105263 8.13948 0.421053 7.53074 1.21053C5.15877 4.28422 2.84977 7.28422 0.530272 10.3053C0.404326 10.4632 0.320363 10.6948 0.309867 10.8948C0.25739 11.579 0.225903 12.2842 0.183922 12.9579V13.0527C0.173426 13.3053 0.152435 13.5474 0.14194 13.8C0.131444 14.0105 0.120949 14.2105 0.110453 14.4211C0.0894626 14.9474 0.0579762 15.4842 0.00549892 16.0105C-0.0259874 16.3158 0.0789671 16.5895 0.288876 16.7369C0.393831 16.8211 0.530272 16.8632 0.677208 16.8632C0.792658 16.8632 0.918603 16.8316 1.04455 16.779C2.36698 16.2105 3.74188 15.6527 5.0748 15.1053ZM1.87369 10.7053L7.3943 3.53685L10.8788 6.18948L5.35818 13.3369L1.87369 10.7053ZM10.8368 1.57895C11.1622 1.78948 11.5085 2.04211 11.8759 2.36842C12.4846 2.89474 12.6105 3.84211 12.1487 4.52632C12.0333 4.70527 11.8968 4.87369 11.7499 5.06316L11.7289 5.09474C11.7079 5.1158 11.6974 5.13685 11.6764 5.1579L8.20245 2.49474C8.24443 2.44211 8.27592 2.4 8.3179 2.34737C8.59078 1.97895 8.85317 1.62106 9.28348 1.45263C9.82924 1.23158 10.354 1.27369 10.8368 1.57895ZM4.1617 14.0632L1.36991 15.2105L1.54833 12.0948L4.1617 14.0632Z" fill="var(--font-color1)"/>
                        <path d="M19.5593 17.0316C19.3704 16.9369 14.7629 14.7263 9.91399 17.0316C5.06509 19.3263 3.1969 18.3263 2.10538 17.2C1.80101 16.8842 1.30772 16.8737 0.992857 17.179C0.677994 17.4842 0.667498 17.979 0.971867 18.2948C2.06339 19.4316 3.39632 20 5.03361 20C6.58693 20 8.42364 19.4842 10.5857 18.4632C14.7419 16.4948 18.8246 18.4421 18.8666 18.4632C19.2549 18.6527 19.7272 18.4948 19.9162 18.1053C20.1156 17.7053 19.9476 17.2316 19.5593 17.0316Z" fill="var(--font-color1)"/>
                      </svg>
                      <svg class="emty_f_hover" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M5.0748 15.1053L5.57859 14.9053C5.80948 14.8105 5.9984 14.6737 6.14534 14.4737C6.51268 13.9895 6.89052 13.5053 7.25786 13.0316L7.63569 12.5474C8.18146 11.8421 8.71673 11.1474 9.26249 10.4421C10.5114 8.82106 11.8129 7.13685 13.0933 5.47369C14.0064 4.28422 13.9645 2.70527 12.9884 1.64211C12.663 1.28421 12.2642 0.989475 11.8759 0.705264C11.8024 0.652632 11.7184 0.589474 11.645 0.536843C11.1622 0.178948 10.5954 0 9.96568 0H9.94469C8.95812 0.0105263 8.13948 0.421053 7.53074 1.21053C5.15877 4.28422 2.84977 7.28422 0.530272 10.3053C0.404326 10.4632 0.320363 10.6948 0.309867 10.8948C0.25739 11.579 0.225903 12.2842 0.183922 12.9579V13.0527C0.173426 13.3053 0.152435 13.5474 0.14194 13.8C0.131444 14.0105 0.120949 14.2105 0.110453 14.4211C0.0894626 14.9474 0.0579762 15.4842 0.00549892 16.0105C-0.0259874 16.3158 0.0789671 16.5895 0.288876 16.7369C0.393831 16.8211 0.530272 16.8632 0.677208 16.8632C0.792658 16.8632 0.918603 16.8316 1.04455 16.779C2.36698 16.2105 3.74188 15.6527 5.0748 15.1053ZM1.87369 10.7053L7.3943 3.53685L10.8788 6.18948L5.35818 13.3369L1.87369 10.7053ZM10.8368 1.57895C11.1622 1.78948 11.5085 2.04211 11.8759 2.36842C12.4846 2.89474 12.6105 3.84211 12.1487 4.52632C12.0333 4.70527 11.8968 4.87369 11.7499 5.06316L11.7289 5.09474C11.7079 5.1158 11.6974 5.13685 11.6764 5.1579L8.20245 2.49474C8.24443 2.44211 8.27592 2.4 8.3179 2.34737C8.59078 1.97895 8.85317 1.62106 9.28348 1.45263C9.82924 1.23158 10.354 1.27369 10.8368 1.57895ZM4.1617 14.0632L1.36991 15.2105L1.54833 12.0948L4.1617 14.0632Z" fill="var(--text-color)"/>
                        <path d="M19.5593 17.0316C19.3704 16.9369 14.7629 14.7263 9.91399 17.0316C5.06509 19.3263 3.1969 18.3263 2.10538 17.2C1.80101 16.8842 1.30772 16.8737 0.992857 17.179C0.677994 17.4842 0.667498 17.979 0.971867 18.2948C2.06339 19.4316 3.39632 20 5.03361 20C6.58693 20 8.42364 19.4842 10.5857 18.4632C14.7419 16.4948 18.8246 18.4421 18.8666 18.4632C19.2549 18.6527 19.7272 18.4948 19.9162 18.1053C20.1156 17.7053 19.9476 17.2316 19.5593 17.0316Z" fill="var(--text-color)"/>
                      </svg>

                    </div> <!-- TODO uncomment if need tooltips-->
                    <!-- <div class="on_hov">
                      редактировать
                    </div> -->
                  </div>
                  <div *ngIf="item.name !== 'Послушать позже'" class="flex items-center cursor-pointer icons_w fav_hov"
                    (click)="removePlaylist(item.id); $event.stopPropagation()">
                    <div class="icon-wrap star_w">
                      <svg class="emty_f" width="18" height="22" viewBox="0 0 18 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10.6234 0C10.7094 0.0286963 10.7955 0.0573925 10.8816 0.0803496C11.7655 0.338616 12.3624 1.11342 12.3796 2.0317C12.3853 2.26127 12.3796 2.49084 12.3796 2.7491C12.4657 2.7491 12.5403 2.7491 12.6149 2.7491C13.7685 2.7491 14.9221 2.7491 16.0699 2.7491C17.1432 2.7491 17.872 3.48373 17.872 4.55697C17.872 5.29159 17.872 6.03196 17.872 6.76658C17.872 7.28885 17.608 7.55286 17.0743 7.5586C16.9882 7.5586 16.9078 7.5586 16.8103 7.5586C16.7758 8.24731 16.7471 8.90732 16.7127 9.57308C16.6037 11.863 16.4946 14.1473 16.3856 16.4372C16.3282 17.6654 16.2823 18.8994 16.2019 20.1276C16.133 21.1664 15.2607 21.9699 14.2219 21.9928C13.7398 22.0043 13.2634 21.9986 12.7813 21.9986C9.79117 21.9986 6.79528 21.9986 3.80513 21.9986C2.72041 21.9986 1.85952 21.3213 1.69308 20.2997C1.62421 19.8578 1.62421 19.3987 1.60125 18.951C1.49221 16.7012 1.3889 14.4457 1.28559 12.1959C1.21672 10.6865 1.14211 9.17707 1.0675 7.66764C1.0675 7.63895 1.05602 7.61599 1.05028 7.56434C0.92402 7.56434 0.809235 7.57008 0.688711 7.56434C0.298441 7.55286 0.00573925 7.27164 0 6.88137C0.00573925 6.04917 0 5.21698 0.0172178 4.39053C0.0344355 3.48947 0.792017 2.76058 1.7103 2.75484C2.89258 2.74336 4.07487 2.75484 5.25142 2.75484C5.32603 2.75484 5.40638 2.75484 5.50968 2.75484C5.50968 2.54249 5.50968 2.34162 5.50968 2.14648C5.51542 1.06176 6.14674 0.275484 7.19703 0.0344355C7.22572 0.0286963 7.24868 0.0114785 7.27164 0C8.39079 0 9.50421 0 10.6234 0ZM2.47362 7.57008C2.47362 7.77095 2.46788 7.94887 2.47362 8.12679C2.58266 10.4225 2.69171 12.7182 2.80076 15.0082C2.87537 16.6151 2.94998 18.2279 3.03033 19.8349C3.05902 20.3858 3.31155 20.6154 3.86252 20.6154C7.24868 20.6154 10.6348 20.6154 14.021 20.6154C14.5834 20.6154 14.8302 20.3858 14.8589 19.8176C14.9508 17.8893 15.0426 15.9551 15.1344 14.0267C15.2148 12.3624 15.2951 10.698 15.3755 9.02785C15.3984 8.54575 15.4214 8.05791 15.4386 7.56434C11.1055 7.57008 6.79528 7.57008 2.47362 7.57008ZM16.5061 6.18118C16.5061 5.65891 16.5061 5.15959 16.5061 4.66027C16.5061 4.21835 16.42 4.13226 15.9838 4.13226C11.2891 4.13226 6.5944 4.13226 1.89969 4.13226C1.85952 4.13226 1.8136 4.13226 1.77343 4.13226C1.5209 4.138 1.3889 4.26427 1.3889 4.51679C1.38316 5.02759 1.3889 5.53264 1.3889 6.04344C1.3889 6.08935 1.40038 6.13526 1.40612 6.18692C6.43944 6.18118 11.4613 6.18118 16.5061 6.18118ZM11.0079 2.74336C11.0079 2.4851 11.0194 2.24979 11.0079 2.01448C10.9907 1.66438 10.7037 1.38316 10.3479 1.37742C9.41812 1.37168 8.48836 1.37168 7.5586 1.37742C7.23146 1.37742 6.93876 1.62995 6.90432 1.95135C6.87563 2.20961 6.89858 2.47362 6.89858 2.74336C8.27027 2.74336 9.62473 2.74336 11.0079 2.74336Z" fill="var(--font-color1)"/>
                        <path d="M6.20236 14.1013C6.20236 15.5763 6.20236 17.0513 6.20236 18.5263C6.20236 19.0371 5.73748 19.3757 5.28408 19.2035C5.01433 19.1002 4.83642 18.8592 4.83068 18.5607C4.82494 18.2451 4.83068 17.9294 4.83068 17.6138C4.83068 14.9737 4.83068 12.3279 4.83068 9.68784C4.83068 9.14262 5.28982 8.79826 5.76043 8.98192C6.0474 9.09096 6.20236 9.34349 6.20236 9.69932C6.20236 11.1628 6.20236 12.6321 6.20236 14.1013Z" fill="var(--font-color1)"/>
                        <path d="M9.63986 14.0841C9.63986 15.5591 9.63986 17.0341 9.63986 18.5091C9.63986 19.0371 9.17498 19.3815 8.71584 19.2036C8.44035 19.1003 8.26818 18.8535 8.26818 18.5378C8.26244 17.9811 8.26818 17.4187 8.26818 16.8619C8.26818 14.4687 8.26818 12.0811 8.26818 9.68788C8.26818 9.14839 8.72732 8.80403 9.20367 8.98769C9.47916 9.09674 9.63986 9.34352 9.63986 9.68214C9.63986 11.0136 9.63986 12.3452 9.63986 13.6767C9.63986 13.8087 9.63986 13.9464 9.63986 14.0841Z" fill="var(--font-color1)"/>
                        <path d="M13.0674 14.1128C13.0674 15.5821 13.0674 17.0456 13.0674 18.5148C13.0674 18.8247 12.9354 19.0543 12.6542 19.1863C12.3844 19.3068 12.1376 19.2552 11.9195 19.06C11.7474 18.9051 11.69 18.7099 11.6957 18.4804C11.7014 17.2866 11.6957 16.0871 11.6957 14.8933C11.6957 13.1543 11.6957 11.4154 11.6957 9.67636C11.6957 9.13687 12.1721 8.79825 12.6369 8.98765C12.9124 9.09669 13.0674 9.34922 13.0674 9.69358C13.0731 11.1628 13.0674 12.6378 13.0674 14.1128Z" fill="var(--font-color1)"/>
                      </svg>
                      <svg class="emty_f_hover" width="18" height="22" viewBox="0 0 18 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10.6234 0C10.7094 0.0286963 10.7955 0.0573925 10.8816 0.0803496C11.7655 0.338616 12.3624 1.11342 12.3796 2.0317C12.3853 2.26127 12.3796 2.49084 12.3796 2.7491C12.4657 2.7491 12.5403 2.7491 12.6149 2.7491C13.7685 2.7491 14.9221 2.7491 16.0699 2.7491C17.1432 2.7491 17.872 3.48373 17.872 4.55697C17.872 5.29159 17.872 6.03196 17.872 6.76658C17.872 7.28885 17.608 7.55286 17.0743 7.5586C16.9882 7.5586 16.9078 7.5586 16.8103 7.5586C16.7758 8.24731 16.7471 8.90732 16.7127 9.57308C16.6037 11.863 16.4946 14.1473 16.3856 16.4372C16.3282 17.6654 16.2823 18.8994 16.2019 20.1276C16.133 21.1664 15.2607 21.9699 14.2219 21.9928C13.7398 22.0043 13.2634 21.9986 12.7813 21.9986C9.79117 21.9986 6.79528 21.9986 3.80513 21.9986C2.72041 21.9986 1.85952 21.3213 1.69308 20.2997C1.62421 19.8578 1.62421 19.3987 1.60125 18.951C1.49221 16.7012 1.3889 14.4457 1.28559 12.1959C1.21672 10.6865 1.14211 9.17707 1.0675 7.66764C1.0675 7.63895 1.05602 7.61599 1.05028 7.56434C0.92402 7.56434 0.809235 7.57008 0.688711 7.56434C0.298441 7.55286 0.00573925 7.27164 0 6.88137C0.00573925 6.04917 0 5.21698 0.0172178 4.39053C0.0344355 3.48947 0.792017 2.76058 1.7103 2.75484C2.89258 2.74336 4.07487 2.75484 5.25142 2.75484C5.32603 2.75484 5.40638 2.75484 5.50968 2.75484C5.50968 2.54249 5.50968 2.34162 5.50968 2.14648C5.51542 1.06176 6.14674 0.275484 7.19703 0.0344355C7.22572 0.0286963 7.24868 0.0114785 7.27164 0C8.39079 0 9.50421 0 10.6234 0ZM2.47362 7.57008C2.47362 7.77095 2.46788 7.94887 2.47362 8.12679C2.58266 10.4225 2.69171 12.7182 2.80076 15.0082C2.87537 16.6151 2.94998 18.2279 3.03033 19.8349C3.05902 20.3858 3.31155 20.6154 3.86252 20.6154C7.24868 20.6154 10.6348 20.6154 14.021 20.6154C14.5834 20.6154 14.8302 20.3858 14.8589 19.8176C14.9508 17.8893 15.0426 15.9551 15.1344 14.0267C15.2148 12.3624 15.2951 10.698 15.3755 9.02785C15.3984 8.54575 15.4214 8.05791 15.4386 7.56434C11.1055 7.57008 6.79528 7.57008 2.47362 7.57008ZM16.5061 6.18118C16.5061 5.65891 16.5061 5.15959 16.5061 4.66027C16.5061 4.21835 16.42 4.13226 15.9838 4.13226C11.2891 4.13226 6.5944 4.13226 1.89969 4.13226C1.85952 4.13226 1.8136 4.13226 1.77343 4.13226C1.5209 4.138 1.3889 4.26427 1.3889 4.51679C1.38316 5.02759 1.3889 5.53264 1.3889 6.04344C1.3889 6.08935 1.40038 6.13526 1.40612 6.18692C6.43944 6.18118 11.4613 6.18118 16.5061 6.18118ZM11.0079 2.74336C11.0079 2.4851 11.0194 2.24979 11.0079 2.01448C10.9907 1.66438 10.7037 1.38316 10.3479 1.37742C9.41812 1.37168 8.48836 1.37168 7.5586 1.37742C7.23146 1.37742 6.93876 1.62995 6.90432 1.95135C6.87563 2.20961 6.89858 2.47362 6.89858 2.74336C8.27027 2.74336 9.62473 2.74336 11.0079 2.74336Z" fill="var(--text-color)"/>
                        <path d="M6.20236 14.1013C6.20236 15.5763 6.20236 17.0513 6.20236 18.5263C6.20236 19.0371 5.73748 19.3757 5.28408 19.2035C5.01433 19.1002 4.83642 18.8592 4.83068 18.5607C4.82494 18.2451 4.83068 17.9294 4.83068 17.6138C4.83068 14.9737 4.83068 12.3279 4.83068 9.68784C4.83068 9.14262 5.28982 8.79826 5.76043 8.98192C6.0474 9.09096 6.20236 9.34349 6.20236 9.69932C6.20236 11.1628 6.20236 12.6321 6.20236 14.1013Z" fill="var(--text-color)"/>
                        <path d="M9.63986 14.0841C9.63986 15.5591 9.63986 17.0341 9.63986 18.5091C9.63986 19.0371 9.17498 19.3815 8.71584 19.2036C8.44035 19.1003 8.26818 18.8535 8.26818 18.5378C8.26244 17.9811 8.26818 17.4187 8.26818 16.8619C8.26818 14.4687 8.26818 12.0811 8.26818 9.68788C8.26818 9.14839 8.72732 8.80403 9.20367 8.98769C9.47916 9.09674 9.63986 9.34352 9.63986 9.68214C9.63986 11.0136 9.63986 12.3452 9.63986 13.6767C9.63986 13.8087 9.63986 13.9464 9.63986 14.0841Z" fill="var(--text-color)"/>
                        <path d="M13.0674 14.1128C13.0674 15.5821 13.0674 17.0456 13.0674 18.5148C13.0674 18.8247 12.9354 19.0543 12.6542 19.1863C12.3844 19.3068 12.1376 19.2552 11.9195 19.06C11.7474 18.9051 11.69 18.7099 11.6957 18.4804C11.7014 17.2866 11.6957 16.0871 11.6957 14.8933C11.6957 13.1543 11.6957 11.4154 11.6957 9.67636C11.6957 9.13687 12.1721 8.79825 12.6369 8.98765C12.9124 9.09669 13.0674 9.34922 13.0674 9.69358C13.0731 11.1628 13.0674 12.6378 13.0674 14.1128Z" fill="var(--text-color)"/>
                      </svg>

                    </div> <!-- TODO uncomment if need tooltips-->
                    <!-- <div class="on_hov">
                      удалить
                    </div> -->
                  </div>
                  <div class="flex items-center cursor-pointer icons_w fav_hov" (click)="play(item.items); $event.stopPropagation()">
                    <div class="icon-wrap star_w">
                      <svg class="emty_f" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6.40263 10.2479C8.21852 10.2479 11.491 10.2479 13.3069 10.2479C13.7952 10.2479 14.1614 10.5368 14.2454 10.9701C14.3217 11.3601 14.0775 11.779 13.6655 11.909C13.5205 11.9596 13.3527 11.974 13.2001 11.974C12.3608 11.9813 11.5215 11.974 10.6746 11.974C7.96607 11.974 3.80851 11.974 1.09994 11.974C0.932081 11.974 0.764226 11.9668 0.61163 11.9163C0.207252 11.7935 -0.0521606 11.3818 0.0088776 10.9846C0.0699158 10.5801 0.420886 10.2696 0.855783 10.2407C0.932081 10.2335 1.00838 10.2407 1.07705 10.2407C2.85479 10.2479 4.62489 10.2479 6.40263 10.2479Z" fill="var(--font-color1)"/>
                        <path d="M6.37913 1.74056C4.60902 1.74056 2.83129 1.74056 1.06118 1.74056C0.923842 1.74056 0.778876 1.73334 0.64154 1.69C0.244792 1.58167 -0.0146208 1.22056 0.00063879 0.823335C0.0235281 0.433334 0.321089 0.101111 0.725468 0.0288889C0.832285 0.00722224 0.946731 0 1.05355 0C4.61665 0 15.3472 0 18.9103 0C19.3604 0 19.7114 0.137223 19.8945 0.54889C20.1539 1.12667 19.7038 1.73334 19.0171 1.74056C17.8497 1.74778 16.69 1.74056 15.5227 1.74056C14.8589 1.74056 7.03529 1.74056 6.37913 1.74056Z" fill="var(--font-color1)"/>
                        <path d="M6.37913 6.8586C4.60902 6.8586 2.83129 6.8586 1.06118 6.8586C0.923842 6.8586 0.778876 6.85138 0.64154 6.80805C0.244792 6.69971 -0.0146208 6.3386 0.00063879 5.94138C0.0235281 5.55138 0.321089 5.21915 0.725468 5.14693C0.832285 5.12526 0.946731 5.11804 1.05355 5.11804C4.61665 5.11804 15.3472 5.11804 18.9103 5.11804C19.3604 5.11804 19.7114 5.25526 19.8945 5.66693C20.1539 6.24471 19.7038 6.85138 19.0171 6.8586C17.8497 6.86582 16.69 6.8586 15.5227 6.8586C14.8589 6.8586 7.03529 6.8586 6.37913 6.8586Z" fill="var(--font-color1)"/>
                        <path d="M3.66601 17.3046C2.75043 17.3046 1.83486 17.3046 0.919288 17.3046C0.49965 17.3046 0.16394 17.0591 0.041863 16.6835C-0.0725837 16.3296 0.0494928 15.9468 0.385203 15.7518C0.553058 15.6507 0.774322 15.5857 0.965066 15.5857C2.76569 15.5713 6.98048 15.5713 8.78111 15.5785C9.35335 15.5785 9.73484 15.9613 9.72721 16.4668C9.71958 16.9579 9.3152 17.3046 8.75822 17.3046C7.87317 17.3118 4.55869 17.3046 3.66601 17.3046Z" fill="var(--font-color1)"/>
                        <g transform="translate(14, 14)">
                          <path class="playpath" d="M0.535869 0C0.586719 0 0.637568 0 0.693502 0C0.846049 0.027483 0.988427 0.0778685 1.12063 0.146576C2.58509 0.906939 4.05463 1.66272 5.51909 2.42308C5.62587 2.47805 5.72757 2.54676 5.81401 2.62463C6.053 2.83533 6.06317 3.10558 5.84961 3.33918C5.75808 3.43996 5.64113 3.51324 5.514 3.57737C4.04955 4.33315 2.58509 5.09352 1.11555 5.8493C1.00877 5.90426 0.891814 5.94549 0.774861 5.97755C0.454511 6.05542 0.18501 5.92717 0.068057 5.64776C0.012123 5.5195 0.00195312 5.38667 0.00195312 5.24925C0.00703804 3.74685 0.00703804 2.24444 0.00703804 0.746621C0.00703804 0.687075 0.00703804 0.627528 0.0172079 0.563401C0.0375475 0.398503 0.0934816 0.247347 0.230774 0.128254C0.317218 0.054966 0.424001 0.0229025 0.535869 0Z" fill="var(--font-color1)"/>
                        </g>
                      </svg>
                      <svg class="emty_f_hover" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6.40263 10.2479C8.21852 10.2479 11.491 10.2479 13.3069 10.2479C13.7952 10.2479 14.1614 10.5368 14.2454 10.9701C14.3217 11.3601 14.0775 11.779 13.6655 11.909C13.5205 11.9596 13.3527 11.974 13.2001 11.974C12.3608 11.9813 11.5215 11.974 10.6746 11.974C7.96607 11.974 3.80851 11.974 1.09994 11.974C0.932081 11.974 0.764226 11.9668 0.61163 11.9163C0.207252 11.7935 -0.0521606 11.3818 0.0088776 10.9846C0.0699158 10.5801 0.420886 10.2696 0.855783 10.2407C0.932081 10.2335 1.00838 10.2407 1.07705 10.2407C2.85479 10.2479 4.62489 10.2479 6.40263 10.2479Z" fill="var(--text-color)"/>
                        <path d="M6.37913 1.74056C4.60902 1.74056 2.83129 1.74056 1.06118 1.74056C0.923842 1.74056 0.778876 1.73334 0.64154 1.69C0.244792 1.58167 -0.0146208 1.22056 0.00063879 0.823335C0.0235281 0.433334 0.321089 0.101111 0.725468 0.0288889C0.832285 0.00722224 0.946731 0 1.05355 0C4.61665 0 15.3472 0 18.9103 0C19.3604 0 19.7114 0.137223 19.8945 0.54889C20.1539 1.12667 19.7038 1.73334 19.0171 1.74056C17.8497 1.74778 16.69 1.74056 15.5227 1.74056C14.8589 1.74056 7.03529 1.74056 6.37913 1.74056Z" fill="var(--text-color)"/>
                        <path d="M6.37913 6.8586C4.60902 6.8586 2.83129 6.8586 1.06118 6.8586C0.923842 6.8586 0.778876 6.85138 0.64154 6.80805C0.244792 6.69971 -0.0146208 6.3386 0.00063879 5.94138C0.0235281 5.55138 0.321089 5.21915 0.725468 5.14693C0.832285 5.12526 0.946731 5.11804 1.05355 5.11804C4.61665 5.11804 15.3472 5.11804 18.9103 5.11804C19.3604 5.11804 19.7114 5.25526 19.8945 5.66693C20.1539 6.24471 19.7038 6.85138 19.0171 6.8586C17.8497 6.86582 16.69 6.8586 15.5227 6.8586C14.8589 6.8586 7.03529 6.8586 6.37913 6.8586Z" fill="var(--text-color)"/>
                        <path d="M3.66601 17.3046C2.75043 17.3046 1.83486 17.3046 0.919288 17.3046C0.49965 17.3046 0.16394 17.0591 0.041863 16.6835C-0.0725837 16.3296 0.0494928 15.9468 0.385203 15.7518C0.553058 15.6507 0.774322 15.5857 0.965066 15.5857C2.76569 15.5713 6.98048 15.5713 8.78111 15.5785C9.35335 15.5785 9.73484 15.9613 9.72721 16.4668C9.71958 16.9579 9.3152 17.3046 8.75822 17.3046C7.87317 17.3118 4.55869 17.3046 3.66601 17.3046Z" fill="var(--text-color)"/>
                        <g transform="translate(14, 14)">
                          <path class="playpath" d="M0.535869 0C0.586719 0 0.637568 0 0.693502 0C0.846049 0.027483 0.988427 0.0778685 1.12063 0.146576C2.58509 0.906939 4.05463 1.66272 5.51909 2.42308C5.62587 2.47805 5.72757 2.54676 5.81401 2.62463C6.053 2.83533 6.06317 3.10558 5.84961 3.33918C5.75808 3.43996 5.64113 3.51324 5.514 3.57737C4.04955 4.33315 2.58509 5.09352 1.11555 5.8493C1.00877 5.90426 0.891814 5.94549 0.774861 5.97755C0.454511 6.05542 0.18501 5.92717 0.068057 5.64776C0.012123 5.5195 0.00195312 5.38667 0.00195312 5.24925C0.00703804 3.74685 0.00703804 2.24444 0.00703804 0.746621C0.00703804 0.687075 0.00703804 0.627528 0.0172079 0.563401C0.0375475 0.398503 0.0934816 0.247347 0.230774 0.128254C0.317218 0.054966 0.424001 0.0229025 0.535869 0Z" fill="var(--text-color)"/>
                        </g>
                      </svg>

                    </div> <!-- TODO uncomment if need tooltips-->
                    <!-- <div class="on_hov">
                      воспроизвести
                    </div> -->
                  </div>
                </div>
                <!-- <div class="playlist-track__delete" (click)="removePlaylist(item.id)">Удалить</div>
                <div class="playlist-play" (click)="play(item.items)">
                  <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M1.13493 0.769531C1.22888 0.769531 1.32284 0.769531 1.42618 0.769531C1.70804 0.823477 1.9711 0.922378 2.21538 1.05724C4.92119 2.54975 7.6364 4.03327 10.3422 5.52578C10.5395 5.63367 10.7274 5.76853 10.8871 5.92138C11.3287 6.33497 11.3475 6.86544 10.9529 7.32398C10.7838 7.52178 10.5677 7.66564 10.3328 7.79151C7.62701 9.27503 4.92119 10.7675 2.20598 12.251C2.00868 12.3589 1.79259 12.4399 1.57651 12.5028C0.984609 12.6556 0.486664 12.4039 0.270575 11.8554C0.167228 11.6037 0.148438 11.343 0.148438 11.0732C0.157833 8.12418 0.157833 5.17513 0.157833 2.23507C0.157833 2.11818 0.157833 2.0013 0.176623 1.87543C0.214204 1.55175 0.317551 1.25505 0.571221 1.02128C0.730939 0.877423 0.928238 0.814486 1.13493 0.769531Z" fill="var(--font-color1)"/>
                    </svg>

                </div>                      -->
              </div>
            </div>
          }
        </div>
      } @else {
        @if (isLoading) {
          <app-loading-indicator text="Загрузка плейлистов..."></app-loading-indicator>
        } @else {
          <div class="text-center p-8">
            <p class="text-lg">Список плейлистов пуст</p>
          </div>
        }
      }
    }

</div>

<ng-template #dropdItemIcon let-action="action" let-item="item">

  <ng-container [ngSwitch]="action">
    <div *ngSwitchCase="'редактировать'">
      <svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M5.0748 15.1053L5.57859 14.9053C5.80948 14.8105 5.9984 14.6737 6.14534 14.4737C6.51268 13.9895 6.89052 13.5053 7.25786 13.0316L7.63569 12.5474C8.18146 11.8421 8.71673 11.1474 9.26249 10.4421C10.5114 8.82106 11.8129 7.13685 13.0933 5.47369C14.0064 4.28422 13.9645 2.70527 12.9884 1.64211C12.663 1.28421 12.2642 0.989475 11.8759 0.705264C11.8024 0.652632 11.7184 0.589474 11.645 0.536843C11.1622 0.178948 10.5954 0 9.96568 0H9.94469C8.95812 0.0105263 8.13948 0.421053 7.53074 1.21053C5.15877 4.28422 2.84977 7.28422 0.530272 10.3053C0.404326 10.4632 0.320363 10.6948 0.309867 10.8948C0.25739 11.579 0.225903 12.2842 0.183922 12.9579V13.0527C0.173426 13.3053 0.152435 13.5474 0.14194 13.8C0.131444 14.0105 0.120949 14.2105 0.110453 14.4211C0.0894626 14.9474 0.0579762 15.4842 0.00549892 16.0105C-0.0259874 16.3158 0.0789671 16.5895 0.288876 16.7369C0.393831 16.8211 0.530272 16.8632 0.677208 16.8632C0.792658 16.8632 0.918603 16.8316 1.04455 16.779C2.36698 16.2105 3.74188 15.6527 5.0748 15.1053ZM1.87369 10.7053L7.3943 3.53685L10.8788 6.18948L5.35818 13.3369L1.87369 10.7053ZM10.8368 1.57895C11.1622 1.78948 11.5085 2.04211 11.8759 2.36842C12.4846 2.89474 12.6105 3.84211 12.1487 4.52632C12.0333 4.70527 11.8968 4.87369 11.7499 5.06316L11.7289 5.09474C11.7079 5.1158 11.6974 5.13685 11.6764 5.1579L8.20245 2.49474C8.24443 2.44211 8.27592 2.4 8.3179 2.34737C8.59078 1.97895 8.85317 1.62106 9.28348 1.45263C9.82924 1.23158 10.354 1.27369 10.8368 1.57895ZM4.1617 14.0632L1.36991 15.2105L1.54833 12.0948L4.1617 14.0632Z" fill="var(--font-color1)"/>
        <path d="M19.5593 17.0316C19.3704 16.9369 14.7629 14.7263 9.91399 17.0316C5.06509 19.3263 3.1969 18.3263 2.10538 17.2C1.80101 16.8842 1.30772 16.8737 0.992857 17.179C0.677994 17.4842 0.667498 17.979 0.971867 18.2948C2.06339 19.4316 3.39632 20 5.03361 20C6.58693 20 8.42364 19.4842 10.5857 18.4632C14.7419 16.4948 18.8246 18.4421 18.8666 18.4632C19.2549 18.6527 19.7272 18.4948 19.9162 18.1053C20.1156 17.7053 19.9476 17.2316 19.5593 17.0316Z" fill="var(--font-color1)"/>
      </svg>
    </div>
    <div *ngSwitchCase="'воспроизвести'">
      <svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M6.40263 10.2479C8.21852 10.2479 11.491 10.2479 13.3069 10.2479C13.7952 10.2479 14.1614 10.5368 14.2454 10.9701C14.3217 11.3601 14.0775 11.779 13.6655 11.909C13.5205 11.9596 13.3527 11.974 13.2001 11.974C12.3608 11.9813 11.5215 11.974 10.6746 11.974C7.96607 11.974 3.80851 11.974 1.09994 11.974C0.932081 11.974 0.764226 11.9668 0.61163 11.9163C0.207252 11.7935 -0.0521606 11.3818 0.0088776 10.9846C0.0699158 10.5801 0.420886 10.2696 0.855783 10.2407C0.932081 10.2335 1.00838 10.2407 1.07705 10.2407C2.85479 10.2479 4.62489 10.2479 6.40263 10.2479Z" fill="var(--font-color1)"/>
        <path d="M6.37913 1.74056C4.60902 1.74056 2.83129 1.74056 1.06118 1.74056C0.923842 1.74056 0.778876 1.73334 0.64154 1.69C0.244792 1.58167 -0.0146208 1.22056 0.00063879 0.823335C0.0235281 0.433334 0.321089 0.101111 0.725468 0.0288889C0.832285 0.00722224 0.946731 0 1.05355 0C4.61665 0 15.3472 0 18.9103 0C19.3604 0 19.7114 0.137223 19.8945 0.54889C20.1539 1.12667 19.7038 1.73334 19.0171 1.74056C17.8497 1.74778 16.69 1.74056 15.5227 1.74056C14.8589 1.74056 7.03529 1.74056 6.37913 1.74056Z" fill="var(--font-color1)"/>
        <path d="M6.37913 6.8586C4.60902 6.8586 2.83129 6.8586 1.06118 6.8586C0.923842 6.8586 0.778876 6.85138 0.64154 6.80805C0.244792 6.69971 -0.0146208 6.3386 0.00063879 5.94138C0.0235281 5.55138 0.321089 5.21915 0.725468 5.14693C0.832285 5.12526 0.946731 5.11804 1.05355 5.11804C4.61665 5.11804 15.3472 5.11804 18.9103 5.11804C19.3604 5.11804 19.7114 5.25526 19.8945 5.66693C20.1539 6.24471 19.7038 6.85138 19.0171 6.8586C17.8497 6.86582 16.69 6.8586 15.5227 6.8586C14.8589 6.8586 7.03529 6.8586 6.37913 6.8586Z" fill="var(--font-color1)"/>
        <path d="M3.66601 17.3046C2.75043 17.3046 1.83486 17.3046 0.919288 17.3046C0.49965 17.3046 0.16394 17.0591 0.041863 16.6835C-0.0725837 16.3296 0.0494928 15.9468 0.385203 15.7518C0.553058 15.6507 0.774322 15.5857 0.965066 15.5857C2.76569 15.5713 6.98048 15.5713 8.78111 15.5785C9.35335 15.5785 9.73484 15.9613 9.72721 16.4668C9.71958 16.9579 9.3152 17.3046 8.75822 17.3046C7.87317 17.3118 4.55869 17.3046 3.66601 17.3046Z" fill="var(--font-color1)"/>
        <g transform="translate(14, 14)">
          <path class="playpath" d="M0.535869 0C0.586719 0 0.637568 0 0.693502 0C0.846049 0.027483 0.988427 0.0778685 1.12063 0.146576C2.58509 0.906939 4.05463 1.66272 5.51909 2.42308C5.62587 2.47805 5.72757 2.54676 5.81401 2.62463C6.053 2.83533 6.06317 3.10558 5.84961 3.33918C5.75808 3.43996 5.64113 3.51324 5.514 3.57737C4.04955 4.33315 2.58509 5.09352 1.11555 5.8493C1.00877 5.90426 0.891814 5.94549 0.774861 5.97755C0.454511 6.05542 0.18501 5.92717 0.068057 5.64776C0.012123 5.5195 0.00195312 5.38667 0.00195312 5.24925C0.00703804 3.74685 0.00703804 2.24444 0.00703804 0.746621C0.00703804 0.687075 0.00703804 0.627528 0.0172079 0.563401C0.0375475 0.398503 0.0934816 0.247347 0.230774 0.128254C0.317218 0.054966 0.424001 0.0229025 0.535869 0Z" fill="var(--font-color1)"/>
        </g>
      </svg>
    </div>
    <div *ngSwitchCase="'добавить в очередь'">
      <svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M6.40263 10.2479C8.21852 10.2479 11.491 10.2479 13.3069 10.2479C13.7952 10.2479 14.1614 10.5368 14.2454 10.9701C14.3217 11.3601 14.0775 11.779 13.6655 11.909C13.5205 11.9596 13.3527 11.974 13.2001 11.974C12.3608 11.9813 11.5215 11.974 10.6746 11.974C7.96607 11.974 3.80851 11.974 1.09994 11.974C0.932081 11.974 0.764226 11.9668 0.61163 11.9163C0.207252 11.7935 -0.0521606 11.3818 0.0088776 10.9846C0.0699158 10.5801 0.420886 10.2696 0.855783 10.2407C0.932081 10.2335 1.00838 10.2407 1.07705 10.2407C2.85479 10.2479 4.62489 10.2479 6.40263 10.2479Z" fill="var(--font-color1)"/>
        <path d="M6.37913 1.74056C4.60902 1.74056 2.83129 1.74056 1.06118 1.74056C0.923842 1.74056 0.778876 1.73334 0.64154 1.69C0.244792 1.58167 -0.0146208 1.22056 0.00063879 0.823335C0.0235281 0.433334 0.321089 0.101111 0.725468 0.0288889C0.832285 0.00722224 0.946731 0 1.05355 0C4.61665 0 15.3472 0 18.9103 0C19.3604 0 19.7114 0.137223 19.8945 0.54889C20.1539 1.12667 19.7038 1.73334 19.0171 1.74056C17.8497 1.74778 16.69 1.74056 15.5227 1.74056C14.8589 1.74056 7.03529 1.74056 6.37913 1.74056Z" fill="var(--font-color1)"/>
        <path d="M6.37913 6.8586C4.60902 6.8586 2.83129 6.8586 1.06118 6.8586C0.923842 6.8586 0.778876 6.85138 0.64154 6.80805C0.244792 6.69971 -0.0146208 6.3386 0.00063879 5.94138C0.0235281 5.55138 0.321089 5.21915 0.725468 5.14693C0.832285 5.12526 0.946731 5.11804 1.05355 5.11804C4.61665 5.11804 15.3472 5.11804 18.9103 5.11804C19.3604 5.11804 19.7114 5.25526 19.8945 5.66693C20.1539 6.24471 19.7038 6.85138 19.0171 6.8586C17.8497 6.86582 16.69 6.8586 15.5227 6.8586C14.8589 6.8586 7.03529 6.8586 6.37913 6.8586Z" fill="var(--font-color1)"/>
        <path d="M3.66601 17.3046C2.75043 17.3046 1.83486 17.3046 0.919288 17.3046C0.49965 17.3046 0.16394 17.0591 0.041863 16.6835C-0.0725837 16.3296 0.0494928 15.9468 0.385203 15.7518C0.553058 15.6507 0.774322 15.5857 0.965066 15.5857C2.76569 15.5713 6.98048 15.5713 8.78111 15.5785C9.35335 15.5785 9.73484 15.9613 9.72721 16.4668C9.71958 16.9579 9.3152 17.3046 8.75822 17.3046C7.87317 17.3118 4.55869 17.3046 3.66601 17.3046Z" fill="var(--font-color1)"/>
        <path d="M16.3557 17.5185C15.4402 17.5185 15.1395 17.5185 14.224 17.5185C13.8043 17.5185 13.4686 17.2729 13.3466 16.8973C13.2321 16.5435 13.3542 16.1607 13.6899 15.9657C13.8577 15.8646 14.079 15.7996 14.2698 15.7996C16.0704 15.7851 17.2484 15.7851 19.049 15.7923C19.6213 15.7923 20.0028 16.1751 19.9951 16.6807C19.9875 17.1718 19.5831 17.5185 19.0262 17.5185C18.1411 17.5257 17.2484 17.5185 16.3557 17.5185Z" fill="var(--font-color1)"/>
        <path d="M15.7613 16.3604C15.7613 15.4448 15.7613 15.1442 15.7613 14.2286C15.7613 13.809 16.0069 13.4733 16.3824 13.3512C16.7363 13.2367 17.1191 13.3588 17.3141 13.6945C17.4152 13.8624 17.4802 14.0836 17.4802 14.2744C17.4947 16.075 17.4947 17.2531 17.4874 19.0537C17.4874 19.6259 17.1047 20.0074 16.5991 19.9998C16.108 19.9922 15.7613 19.5878 15.7613 19.0308C15.7541 18.1457 15.7613 17.2531 15.7613 16.3604Z" fill="var(--font-color1)"/>
      </svg>
    </div>
    <div *ngSwitchCase="'удалить'">
      @if(!selectedPlayList) {
        <svg width="16" height="16" viewBox="0 0 18 22" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M10.6234 0C10.7094 0.0286963 10.7955 0.0573925 10.8816 0.0803496C11.7655 0.338616 12.3624 1.11342 12.3796 2.0317C12.3853 2.26127 12.3796 2.49084 12.3796 2.7491C12.4657 2.7491 12.5403 2.7491 12.6149 2.7491C13.7685 2.7491 14.9221 2.7491 16.0699 2.7491C17.1432 2.7491 17.872 3.48373 17.872 4.55697C17.872 5.29159 17.872 6.03196 17.872 6.76658C17.872 7.28885 17.608 7.55286 17.0743 7.5586C16.9882 7.5586 16.9078 7.5586 16.8103 7.5586C16.7758 8.24731 16.7471 8.90732 16.7127 9.57308C16.6037 11.863 16.4946 14.1473 16.3856 16.4372C16.3282 17.6654 16.2823 18.8994 16.2019 20.1276C16.133 21.1664 15.2607 21.9699 14.2219 21.9928C13.7398 22.0043 13.2634 21.9986 12.7813 21.9986C9.79117 21.9986 6.79528 21.9986 3.80513 21.9986C2.72041 21.9986 1.85952 21.3213 1.69308 20.2997C1.62421 19.8578 1.62421 19.3987 1.60125 18.951C1.49221 16.7012 1.3889 14.4457 1.28559 12.1959C1.21672 10.6865 1.14211 9.17707 1.0675 7.66764C1.0675 7.63895 1.05602 7.61599 1.05028 7.56434C0.92402 7.56434 0.809235 7.57008 0.688711 7.56434C0.298441 7.55286 0.00573925 7.27164 0 6.88137C0.00573925 6.04917 0 5.21698 0.0172178 4.39053C0.0344355 3.48947 0.792017 2.76058 1.7103 2.75484C2.89258 2.74336 4.07487 2.75484 5.25142 2.75484C5.32603 2.75484 5.40638 2.75484 5.50968 2.75484C5.50968 2.54249 5.50968 2.34162 5.50968 2.14648C5.51542 1.06176 6.14674 0.275484 7.19703 0.0344355C7.22572 0.0286963 7.24868 0.0114785 7.27164 0C8.39079 0 9.50421 0 10.6234 0ZM2.47362 7.57008C2.47362 7.77095 2.46788 7.94887 2.47362 8.12679C2.58266 10.4225 2.69171 12.7182 2.80076 15.0082C2.87537 16.6151 2.94998 18.2279 3.03033 19.8349C3.05902 20.3858 3.31155 20.6154 3.86252 20.6154C7.24868 20.6154 10.6348 20.6154 14.021 20.6154C14.5834 20.6154 14.8302 20.3858 14.8589 19.8176C14.9508 17.8893 15.0426 15.9551 15.1344 14.0267C15.2148 12.3624 15.2951 10.698 15.3755 9.02785C15.3984 8.54575 15.4214 8.05791 15.4386 7.56434C11.1055 7.57008 6.79528 7.57008 2.47362 7.57008ZM16.5061 6.18118C16.5061 5.65891 16.5061 5.15959 16.5061 4.66027C16.5061 4.21835 16.42 4.13226 15.9838 4.13226C11.2891 4.13226 6.5944 4.13226 1.89969 4.13226C1.85952 4.13226 1.8136 4.13226 1.77343 4.13226C1.5209 4.138 1.3889 4.26427 1.3889 4.51679C1.38316 5.02759 1.3889 5.53264 1.3889 6.04344C1.3889 6.08935 1.40038 6.13526 1.40612 6.18692C6.43944 6.18118 11.4613 6.18118 16.5061 6.18118ZM11.0079 2.74336C11.0079 2.4851 11.0194 2.24979 11.0079 2.01448C10.9907 1.66438 10.7037 1.38316 10.3479 1.37742C9.41812 1.37168 8.48836 1.37168 7.5586 1.37742C7.23146 1.37742 6.93876 1.62995 6.90432 1.95135C6.87563 2.20961 6.89858 2.47362 6.89858 2.74336C8.27027 2.74336 9.62473 2.74336 11.0079 2.74336Z" fill="var(--font-color1)"/>
          <path d="M6.20236 14.1013C6.20236 15.5763 6.20236 17.0513 6.20236 18.5263C6.20236 19.0371 5.73748 19.3757 5.28408 19.2035C5.01433 19.1002 4.83642 18.8592 4.83068 18.5607C4.82494 18.2451 4.83068 17.9294 4.83068 17.6138C4.83068 14.9737 4.83068 12.3279 4.83068 9.68784C4.83068 9.14262 5.28982 8.79826 5.76043 8.98192C6.0474 9.09096 6.20236 9.34349 6.20236 9.69932C6.20236 11.1628 6.20236 12.6321 6.20236 14.1013Z" fill="var(--font-color1)"/>
          <path d="M9.63986 14.0841C9.63986 15.5591 9.63986 17.0341 9.63986 18.5091C9.63986 19.0371 9.17498 19.3815 8.71584 19.2036C8.44035 19.1003 8.26818 18.8535 8.26818 18.5378C8.26244 17.9811 8.26818 17.4187 8.26818 16.8619C8.26818 14.4687 8.26818 12.0811 8.26818 9.68788C8.26818 9.14839 8.72732 8.80403 9.20367 8.98769C9.47916 9.09674 9.63986 9.34352 9.63986 9.68214C9.63986 11.0136 9.63986 12.3452 9.63986 13.6767C9.63986 13.8087 9.63986 13.9464 9.63986 14.0841Z" fill="var(--font-color1)"/>
          <path d="M13.0674 14.1128C13.0674 15.5821 13.0674 17.0456 13.0674 18.5148C13.0674 18.8247 12.9354 19.0543 12.6542 19.1863C12.3844 19.3068 12.1376 19.2552 11.9195 19.06C11.7474 18.9051 11.69 18.7099 11.6957 18.4804C11.7014 17.2866 11.6957 16.0871 11.6957 14.8933C11.6957 13.1543 11.6957 11.4154 11.6957 9.67636C11.6957 9.13687 12.1721 8.79825 12.6369 8.98765C12.9124 9.09669 13.0674 9.34922 13.0674 9.69358C13.0731 11.1628 13.0674 12.6378 13.0674 14.1128Z" fill="var(--font-color1)"/>
        </svg>
      } @else {
        <svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M9.41339 0C9.80471 0 10.196 0 10.5874 0C10.723 0.015656 10.8587 0.0313121 10.9891 0.0469681C13.1388 0.27659 15.0328 1.0907 16.6451 2.53106C18.3721 4.07579 19.4469 5.99104 19.8487 8.28204C19.9165 8.65779 19.953 9.03875 20 9.4145C20 9.8059 20 10.1973 20 10.5887C19.9843 10.7296 19.9687 10.8705 19.953 11.0114C19.7182 13.1093 18.9565 14.988 17.5373 16.5537C14.8293 19.5335 11.4796 20.5877 7.56112 19.6901C5.26535 19.163 3.4183 17.8792 2.00432 15.9953C0.439025 13.9182 -0.197528 11.5594 0.052919 8.98135C0.261625 6.86256 1.08601 4.98906 2.50522 3.39736C4.04964 1.6491 5.97495 0.563617 8.27594 0.15656C8.65161 0.0887176 9.0325 0.0521868 9.41339 0ZM3.52787 15.2385C7.4359 11.3298 11.3335 7.4314 15.2363 3.52783C12.4135 1.14811 7.5559 0.761927 4.17486 4.05491C1.01818 7.12872 0.877308 11.9977 3.52787 15.2385ZM16.4729 4.76465C12.5648 8.67345 8.66204 12.577 4.76446 16.4754C7.76461 18.9699 12.6066 19.1369 15.8676 15.9013C19.0556 12.7336 19.04 7.89064 16.4729 4.76465Z" fill="var(--font-color1)"/>
        </svg>
      }
    </div>
    <div *ngSwitchCase="'поделиться'">
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M15.9238 7.01009C13.5066 8.21941 11.0965 9.42157 8.66504 10.638C8.9368 11.5325 8.94395 12.427 8.66504 13.3357C11.0822 14.5451 13.4923 15.7544 15.8737 16.9422C16.2456 16.5988 16.5745 16.2267 16.9607 15.9476C19.6353 13.9941 23.4542 15.6113 23.9334 18.8957C24.3481 21.7509 22.0168 24.2268 19.1347 23.9835C17.0894 23.8117 15.3731 22.1516 15.1514 20.1051C15.1085 19.6829 15.1156 19.2464 15.1729 18.8313C15.2086 18.588 15.1442 18.5093 14.9512 18.4163C12.8772 17.3859 10.8105 16.3483 8.73656 15.3107C8.50056 15.1891 8.25741 15.0817 8.02141 14.9458C7.87838 14.8671 7.79972 14.8885 7.6853 15.0102C6.76276 15.9834 5.63283 16.4556 4.28836 16.4413C2.21445 16.4127 0.326467 14.7454 0.0475605 12.6846C-0.288557 10.2588 1.18464 8.12638 3.58752 7.63979C5.16084 7.32494 6.52676 7.79722 7.65669 8.94213C7.80687 9.09241 7.89984 9.10672 8.07862 9.01369C10.3742 7.85447 12.677 6.70955 14.9798 5.56464C15.1585 5.47877 15.2158 5.40005 15.1729 5.18538C14.7295 2.75959 16.4458 0.39105 18.8773 0.047575C21.4232 -0.317367 23.7117 1.45725 23.9763 3.99753C24.2266 6.37323 22.4673 8.57004 20.093 8.8348C18.4196 9.028 17.0751 8.41977 16.0239 7.12458C15.9881 7.09596 15.9595 7.06018 15.9238 7.01009ZM7.14179 11.9976C7.14179 10.5021 5.91174 9.26414 4.42424 9.2713C2.94389 9.27845 1.721 10.4949 1.70669 11.9762C1.69239 13.4574 2.92244 14.7025 4.41709 14.7168C5.90459 14.7311 7.14179 13.5003 7.14179 11.9976ZM16.832 19.5541C16.8248 21.0496 18.0549 22.2876 19.5424 22.2804C21.0227 22.2804 22.2671 21.0353 22.2671 19.5541C22.2599 18.0728 21.0513 16.8635 19.5567 16.8492C18.062 16.8277 16.8391 18.0442 16.832 19.5541ZM19.5424 7.14605C21.037 7.14605 22.2671 5.91527 22.2671 4.42688C22.2599 2.93849 21.0156 1.70055 19.5352 1.7077C18.0477 1.71486 16.8463 2.93133 16.832 4.41972C16.8248 5.92242 18.0406 7.14605 19.5424 7.14605Z"
          fill="var(--font-color1)" />
      </svg>
    </div>
    <div *ngSwitchCase="'мне нравится'">
      <div class="flex items-center icons_w lik_hov"
        [ngClass]="{'is-liked': isLiked(item)}">
        <div class="icon-wrap like_w">
          <svg width="16" height="15" viewBox="0 0 24 22" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
              d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
              fill="var(--font-color1)" />
          </svg>
        </div>

      </div>
    </div>

  </ng-container>
</ng-template>
