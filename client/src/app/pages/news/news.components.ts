import { BreadcrumbComponent } from "@/components/breadcrumb/breadcrumb.component";
import { CommonModule, isPlatformBrowser, NgOptimizedImage } from "@angular/common";
import { Component, DestroyRef, ElementRef, inject, PLATFORM_ID, ViewChild } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import {environment} from "@/env/environment";
import { ToasterService } from "@/services/toaster.service";
import { TranslocoService } from "@jsverse/transloco";
import { AdvertisingService } from "@/services/advertising.service";
import { DomSanitizer, SafeResourceUrl } from "@angular/platform-browser";

@Component({
    selector: 'app-news',
    standalone: true,
    imports: [
      CommonModule,
      BreadcrumbComponent,
      NgOptimizedImage
    ], 
    templateUrl: './news.components.html',
    styleUrl: './news.components.scss'
  })
  export class NewsComponent {
    protected readonly environment = environment;
    toasterService = inject(ToasterService);
    advertisingService = inject(AdvertisingService);
    translocoService = inject(TranslocoService);
    route = inject(ActivatedRoute);
    platformId = inject(PLATFORM_ID);
    private readonly destroyRef = inject(DestroyRef);
   
  
    ngOnInit() {
        if (isPlatformBrowser(this.platformId)) {
            this.advertisingService.getAll().subscribe(
                {
                    next: (res: any) => {
                    console.log('calendar', res); 
                    this.advertisings = res.filter((ad: any) => ad.active === true);
                    // Инициализируем счетчик показов для каждой рекламы
                    this.advertisings.forEach(ad => {
                        this.adShownCounts.set(ad.id, 0);
                    });
                    // Запускаем показ рекламы через 20 секунд
                    this.startAdDisplay();    },
                    error: (err) => {
                    }
                }
            );
        }
    }

    
  sanitizer = inject(DomSanitizer);
  @ViewChild('adDialog') adDialog!: ElementRef<HTMLDialogElement>;
  advertisings: any[] = [];
  currentAd: any = null;
  adIntervalId: any = null;
  adTimerId: any = null;
  adShownCounts: Map<number, number> = new Map(); // Для отслеживания количества показов каждой рекламы
  safeAdLink: SafeResourceUrl | null = null;
  startAdDisplay() {
    // Показываем первую рекламу через 20 секунд
    this.adTimerId = setTimeout(() => {
      this.showNextAd();
      
      // Затем показываем рекламу каждые 30 секунд
      this.adIntervalId = setInterval(() => {
        this.showNextAd();
      }, 30000); // 30 секунд
    }, 20000); // 20 секунд
  }

  showNextAd() {
    if (!this.advertisings || this.advertisings.length === 0) return;
    
    // Находим рекламу, которая еще не достигла своего лимита показов (freq)
    const availableAds = this.advertisings.filter(ad => {
      const shownCount = this.adShownCounts.get(ad.id) || 0;
      return shownCount < ad.freq;
    });
    
    if (availableAds.length === 0) {
      // Все рекламы достигли своего лимита показов
      this.clearAdTimers();
      console.log('All ads have reached their frequency limit');
      return;
    }
    
    // Выбираем случайную рекламу из доступных
    const randomIndex = Math.floor(Math.random() * availableAds.length);
    this.currentAd = availableAds[randomIndex];
    
    // Увеличиваем счетчик показов для этой рекламы
    const currentCount = this.adShownCounts.get(this.currentAd.id) || 0;
    this.adShownCounts.set(this.currentAd.id, currentCount + 1);
    
    console.log(`Showing ad ${this.currentAd.id}: ${currentCount + 1}/${this.currentAd.freq} times`);
    
    // Подготавливаем безопасную ссылку для отображения
    this.safeAdLink = this.sanitizer.bypassSecurityTrustResourceUrl(this.currentAd.link);
    
    // Показываем диалоговое окно с рекламой
    if (this.adDialog && isPlatformBrowser(this.platformId)) {
      this.adDialog.nativeElement.showModal();
    }
    
    // Проверяем, нужно ли остановить показ рекламы после этого показа
    if (this.adShownCounts.get(this.currentAd.id) === this.currentAd.freq) {
      console.log(`Ad ${this.currentAd.id} has reached its frequency limit of ${this.currentAd.freq}`);
    }
    
    // Если все рекламы достигли своего лимита, останавливаем таймеры
    const allAdsReachedLimit = this.advertisings.every(ad => 
      (this.adShownCounts.get(ad.id) || 0) >= ad.freq
    );
    
    if (allAdsReachedLimit) {
      console.log('All ads have reached their frequency limits, stopping ad display');
      this.clearAdTimers();
    }
  }
  
  closeAdDialog() {
    if (this.adDialog) {
      this.adDialog.nativeElement.close();
    }
  }

  navigateToAdLink() {
    if (isPlatformBrowser(this.platformId) && this.currentAd) {
      window.open(this.currentAd.link, '_blank');
      this.closeAdDialog();
    }
  }
  ngOnDestroy() {
    this.clearAdTimers();
  }

  clearAdTimers() {
    if (this.adTimerId) {
      clearTimeout(this.adTimerId);
      this.adTimerId = null;
    }
    if (this.adIntervalId) {
      clearInterval(this.adIntervalId);
      this.adIntervalId = null;
    }
  }

  getSafeUrl(url: string): SafeResourceUrl {
    return this.sanitizer.bypassSecurityTrustResourceUrl(url);
  }

  navigateToLink(link: string) {
    if (isPlatformBrowser(this.platformId)) {
      window.open(link, '_blank');
    }
  }
}