import { Component, inject, PLATFORM_ID } from '@angular/core';
import { ForumService } from "@/services/forum.service";
import { CommonModule, isPlatformBrowser, NgOptimizedImage } from "@angular/common";
import { environment } from "@/env/environment";
import { RouterLink } from "@angular/router";
import { BreadcrumbComponent } from '@/components/breadcrumb/breadcrumb.component';

@Component({
  selector: 'app-forum',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    BreadcrumbComponent,
    NgOptimizedImage
  ],
  templateUrl: './forum.component.html',
  styleUrl: './forum.component.scss'
})
export class ForumComponent {
  forumService = inject(ForumService);
  platformId = inject(PLATFORM_ID);
  categories: any = []

  ngOnInit() {
    if (isPlatformBrowser(this.platformId)) {
      this.forumService.getCategories().subscribe(res => this.categories = res);
    }
  }

  nextPage() {

  }

  protected readonly environment = environment;
}
