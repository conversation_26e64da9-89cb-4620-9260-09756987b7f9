import {Component, inject} from '@angular/core';
import {ContentService} from "@/services/content.service";
import {ActivatedRoute, Router} from "@angular/router";
import { NgIf, NgOptimizedImage} from "@angular/common";
import {TranslocoService} from "@jsverse/transloco";
import {environment} from "@/env/environment";

@Component({
  selector: 'app-content-list',
  standalone: true,
  imports: [
    NgIf,
    NgOptimizedImage
  ],
  templateUrl: './content-list.component.html',
  styleUrl: './content-list.component.css'
})
export class ContentListComponent {
  route = inject(ActivatedRoute)
  router = inject(Router)
  translocoService = inject(TranslocoService)
  contentService = inject(ContentService);
  categoryId = this.route.snapshot.params['id'];
  content: any

  ngOnInit() {
    this.contentService.getAll(this.categoryId).subscribe(res => this.content = res);
  }

  protected readonly environment = environment;
}
