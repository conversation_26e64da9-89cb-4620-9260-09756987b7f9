.forgot-page {
    height: 843px;
    max-width: 490px;
    padding: 0 20px;
    margin: 0 auto;
    .forgot-title {
        font-family: Prata;
        font-weight: 400;
        font-size: 24px;
        line-height: 29px;
        letter-spacing: 0;
        text-align: center;
        color: var(--font-color1);
    }
    form {
        width: 100%;
        .form-control {
            display: flex;
            flex-direction: column;
            gap: 6px;
            &:last-of-type {
                margin-bottom: 40px;
            }
            label {
                font-family: Prata;
                font-weight: 400;
                font-size: 14px;
                line-height: 14px;
                letter-spacing: 0;
                color: var(--text-color);
            }
            input {
                width: 450px;
                max-width: 100%;
                height: 50px;
                border-radius: 15px;
                outline: none;
                padding: 13px 25px;
                border: 1px solid var(--text-color);
                background: transparent;
                margin: 0 auto;
                font-family: Prata;
                font-weight: 400;
                font-size: 20px;
                line-height: 24px;
                letter-spacing: 0;
                color: var(--font-color1);
            }
        }

        .submit-button {
            background: url(assets/images/login-button_1.svg);
            width: 353px;
            height: 50px;
            cursor: pointer;
            font-family: Prata;
            font-weight: 400;
            font-size: 20px;
            line-height: 20px;
            letter-spacing: 0;
            text-align: center;
            vertical-align: middle;
            padding: 15px 25px;
            color: var(--font-color1);
            margin: 0 auto;
        }
        .message-box { 
            display: block;
            font-family: Prata;
            font-weight: 400;
            font-size: 20px;
            line-height: 24px;
            letter-spacing: 0;
            text-align: center;
            color: var(--font-color1);
        }
    }
}

@media (max-width: 768px) {
    .forgot-page {
        zoom: 0.9;
    }
}
@media (max-width: 500px) {
    .forgot-page {
        zoom: 0.8;
    }
}