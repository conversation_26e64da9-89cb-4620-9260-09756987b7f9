<div class="admin-component">
  <!-- Page Header -->
  <div class="admin-page-header">
    <div>
      <h1 class="admin-page-title">Контент</h1>
    </div>
    <div class="admin-actions">
      <button type="button" class="btn btn-primary" [routerLink]="['/content/add']">Создать страницу</button>
      <button type="button" class="btn btn-outline-primary" [routerLink]="['/category/add']">Создать категорию</button>
      <button type="button" class="btn btn-outline-secondary" (click)="toggleAll()">
        {{ isExpanded ? 'Свернуть все' : 'Развернуть все' }}
      </button>
      <button type="button" class="btn" [class.btn-primary]="showOnlyActive" [class.btn-outline-primary]="!showOnlyActive" (click)="toggleActiveFilter()">
        {{ showOnlyActive ? 'Показать все' : 'Показать не опубликованные' }}
      </button>
    </div>
  </div>

  <!-- Filters -->
  <div class="admin-filters">
    <div class="admin-filter-item">
      <label class="admin-filter-label">Поиск</label>
      <input type="text" class="form-input" placeholder="Поиск по названию или автору" (input)="search($event)">
    </div>
  </div>

  @if(contentService.tree()) {
    <!-- Modals -->
    <dialog #confirmDialog class="admin-modal">
      <div class="admin-modal-content">
        <div>{{ message }}</div>
        <div class="admin-modal-footer">
          <button class="btn btn-danger">Да</button>
          <button class="btn btn-outline-secondary">Отмена</button>
        </div>
      </div>
    </dialog>

    <dialog #dialog class="admin-modal">
      <div class="admin-modal-content">
        <div>{{message}}</div>
        <div class="admin-modal-footer">
          <button (click)="dialog.close()" class="btn btn-primary">Да</button>
        </div>
      </div>
    </dialog>

    <!-- Content -->
    <div class="admin-content-wrapper admin-table">


  <p-table
  [value]="filtered" dataKey="id"
  [tableStyle]="{ 'min-width': '60rem' }"
  [expandedRowKeys]="getExpandedRowKeys()">
    <ng-template #header>
        <tr>
            <th style="width: 5rem"></th>
            <th>Категория</th>
          <th style="width: 8rem">Сортировка</th>
          <th style="width: 5rem"></th>
            <th style="width: 5rem"></th>
        </tr>
    </ng-template>
    <ng-template #body let-category let-expanded="expanded" let-i="rowIndex">
        <tr (click)="toggleRow(category, $event)">
          <td>
            <i [class]="isRowExpanded(category.id) ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"></i>
          </td>
            <td>{{ category.title }}</td>
          <td>
            <div class="flex gap-2 justify-center">
              <button
                type="button"
                (click)="$event.stopPropagation(); moveCategory('up', i)"
                [disabled]="i === 0 || query || showOnlyActive">
                ↑
              </button>
              <button
                (click)="$event.stopPropagation(); moveCategory('down', i)"
                [disabled]="i === filtered.length - 1 || query || showOnlyActive">
                ↓
              </button>
            </div>
          </td>
            <td>
              <i (click)="$event.stopPropagation();editCategory(category.id)"  class="pi pi-file-edit"></i>
            </td>
            <td>
              <i (click)="$event.stopPropagation();deleteCategory(category.id)"  class="pi pi-trash"></i>
            </td>
        </tr>
    </ng-template>
    <ng-template #expandedrow let-category>
        <tr>
            <td colspan="7">
                <div class="p-4">
                    <p-table [value]="category.contents" dataKey="id">
                        <ng-template #header>
                            <tr>
                                <th pSortableColumn="title">Название <p-sortIcon field="title" /></th>
                                <th pSortableColumn="author">Автор <p-sortIcon field="author" /></th>
                                <th pSortableColumn="views">Показы <p-sortIcon field="views" /></th>
                                <th pSortableColumn="likes">Лайки <p-sortIcon field="likes" /></th>
                                <th pSortableColumn="active">Опубликовано<p-sortIcon field="active" /></th>
                                <th pSortableColumn="created_at">Создание <p-sortIcon field="created_at" /></th>
                            </tr>
                        </ng-template>
                        <ng-template #body let-content>
                            <tr (click)="router.navigate(['/content/' + content.slug])">
                              <td style="width: 40%">{{ content.title }}</td>
                              <td style="width: 17%">{{ content.author }}</td>
                              <td style="width: 10%">{{ content.views }}</td>
                              <td style="width: 10%">{{ content.likes.length }}</td>
                              <td style="width: 8%">{{ content.active  ? "Да" : "Нет" }}</td>
                              <td style="width: 15%">{{ content.created_at  | date: 'dd/MM/yyyy' }}</td>
                            </tr>
                        </ng-template>
                        <ng-template #emptymessage>
                            <tr>
                                <td colspan="6">Нет данных</td>
                            </tr>
                        </ng-template>
                    </p-table>
                </div>
            </td>
        </tr>
      </ng-template>
    </p-table>
    </div>
  }
</div>
